#include "framelesswindow.h"
#include <QPainter>
#include <QPainterPath>
#include <QMouseEvent>
#include <QLayout>
#include <QLabel>
#include <QPushButton>
#include <QTimer>
#include "globalfont.h"
#include <QGraphicsDropShadowEffect>
#include <qdialog.h>
#include <qevent.h>

FramelessWindow::FramelessWindow(QWidget *parent)
    : QDialog{parent}, m_movable(true), m_resizable(true), m_mousePressed(false), m_resizing(false), m_resizeRegion(None), 
     m_goalRegion(None), m_shadow(new QGraphicsDropShadowEffect(this))
{
    setParent(parent);
    setAttribute(Qt::WA_TranslucentBackground);
    setAttribute(Qt::WA_Hover);
#ifdef Q_OS_MACOS
    setAttribute(Qt::WA_MacAlwaysShowToolWindow, true);
#endif
    m_mainLayout = new QVBoxLayout(this);
    m_contentWidget = new QFrame(this);
    m_contentWidget->installEventFilter(this);
    m_contentWidget->setObjectName("contentWidget");
    m_contentWidget->setStyleSheet("QFrame#contentWidget{border:1px solid rgb(70,70,70);}");
    m_mainLayout->setSpacing(0);
    m_mainLayout->setContentsMargins(m_shadowRadius, m_shadowRadius, m_shadowRadius, m_shadowRadius);
    m_mainLayout->addWidget(m_contentWidget);

    m_contentWidgetLayout = new QVBoxLayout(m_contentWidget);
    m_contentWidgetLayout->setSpacing(0);
    m_contentWidgetLayout->setContentsMargins(0, 0, 0, 0);
    m_titleBar = new QWidget(this);
    m_contentWidgetLayout->addWidget(m_titleBar);
    m_centralWidgetLayout = new QVBoxLayout();
    m_centralWidgetLayout->setSpacing(0);
    m_centralWidgetLayout->setContentsMargins(0, 0, 0, 0);
    m_contentWidgetLayout->addLayout(m_centralWidgetLayout);
    setHRatio(1, 10);

    m_titleLabel = new QLabel("", m_titleBar);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_titleLabel->setStyleSheet("background:transparent");

    m_minButton = new QPushButton(m_titleBar);
    m_minButton->setMinimumSize(1,1);
    m_minButton->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    m_minButton->setStyleSheet("QPushButton { border: none; background: transparent; image: url(:/Icon/min.svg); }");
    connect(m_minButton, &QPushButton::clicked, this, [this](){
        showMinimized();
        emit buttonClicked(WindowButton_Min);
    });
    m_minButton->hide();

    m_closeButton = new QPushButton(m_titleBar);
    m_closeButton->setMinimumSize(1,1);
    m_closeButton->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    m_closeButton->setStyleSheet("QPushButton { border: none; background: transparent; image: url(:/Icon/close.svg); }");
    connect(m_closeButton, &QPushButton::clicked, this, [this](){
        done(m_closeButtonReturnCode);
        emit buttonClicked(WindowButton_Close);
    });

    m_shadow->setBlurRadius(m_shadowRadius);
    m_shadow->setColor(m_centralWidgetBackground);
    m_shadow->setOffset(0, 0);
    m_contentWidget->setGraphicsEffect(m_shadow);
}

void FramelessWindow::setMovable(bool movable)
{
    this->m_movable = movable;
}

void FramelessWindow::setResizable(bool resizable)
{
    this->m_resizable = resizable;
}

void FramelessWindow::setRightBottomDraggable()
{
    this->m_goalRegion = (ResizeRegion)Right | Bottom;
}

void FramelessWindow::setTitle(const QString &title)
{
    m_titleLabel->setText(title);
}

void FramelessWindow::setCentralWidget(QWidget *widget)
{
    if(!widget)
        return;
    if(m_centralWidget)
        m_centralWidget->deleteLater();
    m_centralWidget = widget;
    m_centralWidgetLayout->addWidget(m_centralWidget);
}

void FramelessWindow::setHRatio(int titleHRatio, int centralHRatio)
{
    m_contentWidgetLayout->setStretch(0, titleHRatio);
    m_contentWidgetLayout->setStretch(1, centralHRatio);
}

void FramelessWindow::setTitleColor(const QColor &color)
{
    m_titleColor = color;
    update();
}

void FramelessWindow::setTitleBackground(const QColor &background)
{
    m_titleBackground = background;
    update();
}

void FramelessWindow::setFont(const QFont &font)
{
    m_titleLabel->setFont(font);
}

void FramelessWindow::setMode(Mode mode)
{
    if(mode == Mode_Normal){
        m_titleBar->setVisible(true);
    }else if(mode == Mode_Custom){
        m_titleBar->setVisible(false);
    }
}
void FramelessWindow::setCloseButtonReturnCode(int code)
{
    m_closeButtonReturnCode = code;
}

int FramelessWindow::getCloseButtonReturnCode()
{
    return m_closeButtonReturnCode;
}

void FramelessWindow::setShadowRadius(int radius){
    m_shadow->setBlurRadius(radius);
}

void FramelessWindow::setShadowOffset(const QPoint& offset)
{
    m_shadow->setOffset(offset);
}

void FramelessWindow::setShadowColor(const QColor& color)
{
    m_shadow->setColor(color);
}

void FramelessWindow::setShadowVisible(bool isVisible)
{
    m_shadow->setEnabled(isVisible);
}

void FramelessWindow::setMinimumSize(int w, int h)
{
    QDialog::setMinimumSize(w, h);
    m_minimumSize = {w, h};
}

void FramelessWindow::setGeometry(const QRect &rect)
{
    QDialog::setGeometry(rect);
    resize(rect.width(), rect.height());
}

void FramelessWindow::setGeometry(int x, int y, int w, int h)
{
    setGeometry({x,y,w,h});
}

void FramelessWindow::resize(int w, int h)
{
    setFixedSize(w, h);
}

void FramelessWindow::setParent(QWidget *parent)
{
    QDialog::setParent(parent);
#ifdef Q_OS_MACOS
    setWindowFlags(Qt::FramelessWindowHint | Qt::Tool);
#elif defined(Q_OS_WIN)
    setWindowFlags(Qt::FramelessWindowHint | Qt::Dialog);
#endif
}

void FramelessWindow::setRestore(bool isRestore)
{
    m_isRestore = isRestore;
}

void FramelessWindow::restoreWindow()
{
    if(m_isRestore) {
        m_isRestore = false;
        show();
        raise();
    }
}

void FramelessWindow::setTitleAlign(TitleAlign align)
{
    m_titleAlign = align;
    switch(align) {
        case TitleAlign_Left:
            m_titleLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
            break;
        case TitleAlign_Center:
            m_titleLabel->setAlignment(Qt::AlignCenter);
            break;
        case TitleAlign_Right:
            m_titleLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
            break;
    }
    adjustGeometry();
}

void FramelessWindow::setWindowButton(WindowButton button)
{
    m_windowButton = button;
    m_minButton->setVisible(button & WindowButton_Min);
    m_closeButton->setVisible(button & WindowButton_Close);
    adjustGeometry();
}

bool FramelessWindow::event(QEvent* event){
    auto type = event->type();
    if(type == QEvent::HoverMove){
        QHoverEvent* e = static_cast<QHoverEvent*>(event);
        do{
            if(!m_mousePressed){
                updateCursorShape(e->position());
                break;
            }
            QPointF delta = e->globalPosition() - m_mousePressPos;

            if(m_resizing) {
                    QRect newRect = m_windowRect;
                    if(m_resizeRegion & Left)
                        newRect.setLeft(newRect.left() + delta.x());
                    if(m_resizeRegion & Right)
                        newRect.setRight(newRect.right() + delta.x());
                    if(m_resizeRegion & Top)
                        newRect.setTop(newRect.top() + delta.y());
                    if(m_resizeRegion & Bottom)
                        newRect.setBottom(newRect.bottom() + delta.y());
                    if(newRect.width() < m_minimumSize.width())
                        newRect.setWidth(m_minimumSize.width());
                    if(newRect.height() < m_minimumSize.height())
                        newRect.setHeight(m_minimumSize.height());
                    if(m_goalRegion){
                        newRect.setWidth(newRect.height()*m_minimumSize.width()/m_minimumSize.height());
                    }
                    move(newRect.topLeft());
                    setFixedSize(newRect.size());
                } else if(m_movable){
                    QPointF pos = m_windowRect.topLeft() + delta;
                    move(pos.x(), pos.y());
            }
        } while(false);
    }
    return QDialog::event(event);
}

void FramelessWindow::mousePressEvent(QMouseEvent *event){
    if(event->button() == Qt::LeftButton) {
        m_mousePressed = true;
        m_mousePressPos = event->globalPosition();
        m_windowRect = geometry();

        if(m_resizeRegion != None && m_resizable) {
            m_resizing = true;
        } else {
            m_resizing = false;
        }
    }
    QWidget::mousePressEvent(event);
}

void FramelessWindow::mouseReleaseEvent(QMouseEvent *event){
    m_mousePressed = false;
    m_resizing = false;
    m_resizeRegion = None;
    unsetCursor();
    QWidget::mouseReleaseEvent(event);
}

void FramelessWindow::resizeEvent(QResizeEvent *event)
{
    adjustGeometry();
}

void FramelessWindow::showEvent(QShowEvent *event)
{
    adjustGeometry();
}

void FramelessWindow::adjustGeometry()
{
    int titleHeight = m_titleBar->height();
    int titleWidth = m_titleBar->width();
    int buttonHeight = titleHeight * 0.9;
    int buttonWidth = buttonHeight / m_closeButton->minimumHeight() * m_closeButton->minimumWidth();
    int buttonY = (m_titleBar->height() - buttonHeight) / 2;
    int buttonX = titleWidth;
    if (m_windowButton &  WindowButton_Close && m_closeButton->isVisible()) {
        buttonX = buttonX - buttonWidth - buttonY;
        m_closeButton->setGeometry(buttonX,  buttonY, buttonWidth, buttonHeight);
    }
    if (m_windowButton & WindowButton_Min && m_minButton->isVisible()) {
        buttonX = buttonX - buttonWidth - buttonY;
        m_minButton->setGeometry(buttonX, buttonY, buttonWidth, buttonHeight);
    }
    int allButonWidth = titleWidth - buttonX;

    int labelX = 5*buttonY;
    switch(m_titleAlign) {
        case TitleAlign_Left:
            m_titleLabel->setGeometry(labelX, 0, titleWidth - allButonWidth - 2*labelX, m_titleBar->height());
            break;
        case TitleAlign_Center:
            m_titleLabel->setGeometry(allButonWidth, 0, titleWidth-2*allButonWidth, m_titleBar->height());
            break;
        case TitleAlign_Right:
            m_titleLabel->setGeometry(labelX, 0, titleWidth - allButonWidth - 2*labelX, m_titleBar->height());
            break;
    }
    QFont font = m_titleLabel->font();
    font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, m_titleLabel->height())*90/100);
    m_titleLabel->setFont(font);

    m_borderRadius = m_titleBar->height()*0.2;
    setStyleSheet(QString("QWidget{background:%1;border-radius:%2px;}").arg(m_centralWidgetBackground.name()).arg(m_borderRadius));
    m_titleBar->setStyleSheet(QString("background: %1; color: %2;").arg(m_titleBackground.name()).arg(m_titleColor.name()));
}

void FramelessWindow::updateResizeRegion(const QPointF &pos)
{
    QRect rect = this->rect();
    m_resizeRegion = None;

    if(pos.x() < m_edge)
        m_resizeRegion = (ResizeRegion)(m_resizeRegion | Left);
    if(pos.x() > rect.width()- m_edge)
        m_resizeRegion = (ResizeRegion)(m_resizeRegion | Right);
    if(pos.y() < m_edge)
        m_resizeRegion = (ResizeRegion)(m_resizeRegion | Top);
    if(pos.y() > rect.height() - m_edge)
        m_resizeRegion = (ResizeRegion)(m_resizeRegion | Bottom);

    if (m_goalRegion != None && m_resizeRegion != m_goalRegion) {
        m_resizeRegion = None;
    }
}

void FramelessWindow::updateCursorShape(const QPointF &pos)
{
    if(!m_resizable)
        return;
    updateResizeRegion(pos);
    if(m_resizeRegion == (Left | Top) || m_resizeRegion == (Right | Bottom))
        setCursor(Qt::SizeFDiagCursor);
    else if(m_resizeRegion == (Right | Top) || m_resizeRegion == (Left | Bottom))
        setCursor(Qt::SizeBDiagCursor);
    else if(m_resizeRegion == Left || m_resizeRegion == Right)
        setCursor(Qt::SizeHorCursor);
    else if(m_resizeRegion == Top || m_resizeRegion == Bottom)
        setCursor(Qt::SizeVerCursor);
    else
        unsetCursor();
}
