#ifndef FIELDMIXERBASE1_H
#define FIELDMIXERBASE1_H


#include <QPair>
#include <QFont>
#include <QRect>
#include <QLabel>
#include <QColor>
#include <QWidget>
#include <QVector>
#include <QString>
#include <QPainter>
#include <QScrollArea>
#include <QPaintEvent>
#include <QPushButton>
#include <QResizeEvent>

#include "mixerbase.h"
#include "buttonboxs1m1.h"


class FieldMixerBase1 : public QWidget
{
    Q_OBJECT
public:
    explicit FieldMixerBase1(QWidget* parent=nullptr);
    ~FieldMixerBase1();
    struct MixerInfo
    {
        QString name;
        QString text;
        QColor colorDefault;
        QColor colorHovered;
        QColor colorSelected;
    };
    FieldMixerBase1& modifyMixerList(QVector<MixerInfo> list);
    FieldMixerBase1& modifyWidgetList(QVector<MixerBase*> list);
    FieldMixerBase1& setMixer(QString mixer);
    FieldMixerBase1& setFont(QFont font);
    FieldMixerBase1& setVisibleList(QVector<QString> list);
    FieldMixerBase1& setFieldTitle(QString text);
    FieldMixerBase1& setFieldColor(QColor color);
    FieldMixerBase1& setWidgetAreaColor(QColor color);
    FieldMixerBase1& setWidgetAreaVisible(bool state=true);
    FieldMixerBase1& setFieldHeadAreaStretchFactor(float factor);
    FieldMixerBase1& setAdditionVisible(bool state=true);
    FieldMixerBase1& setAdditionButtonWeight(int weightWidth, int weightHeight);
    QColor getSelectedColorByMixerName(QString mixerName) { return mMixerView.value(mixerName).colorSelected; }
    QColor getSelectedColorByMixerText(QString mixerText);
    QVector<QString> getSupportedMixerName() { return mMixerList; }
    QVector<QString> getSupportedMixerText();
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
private:
    struct MixerWidget
    {
        bool visible;
        MixerBase* widget;
    };
    struct MixerView
    {
        QLabel* label;
        QColor colorDefault;
        QColor colorHovered;
        QColor colorSelected;
    };
    QFont mFont;
    float mHeadStretchFactor=5.3;
    QColor mColorBG=QColor(31, 31, 31);
    QColor mColorWidgetArea=QColor(128, 128, 128);
    QRect mRectHead;
    QRect mRectBody;
    QRect mRectWidgetArea;
    QLabel mLabelTitle;
    QVector<QString> mMixerList;
    QMap<QString, MixerView> mMixerView;
    QLabel* mLabelSelected=nullptr;
    QLabel* mLabelHovered=nullptr;
    QScrollArea mScrollArea;
    int mScrollBarValue=0;
    QWidget mWidget;
    QVector<MixerWidget*> mWidgetList;
    ButtonBoxS1M1 mWidgetAddition;
    bool mWidgetAreaVisible=true;
    bool mAdditionVisible=true;
    void drawBG(QPainter* painter);
    void drawWidgetArea(QPainter* painter);
private slots:
    void in_mScrollArea_valueChanged(int value);
    void in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value);
    void in_mWidgetAddition_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDMIXERBASE1_H

