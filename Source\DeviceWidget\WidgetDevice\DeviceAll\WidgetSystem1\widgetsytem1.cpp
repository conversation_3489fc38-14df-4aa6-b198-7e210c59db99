#include "widgetsytem1.h"
#include "appsettings.h"
#include "ui_widgetsytem1.h"
#include "globalfont.h"
#include <QSettings>
#include "autostartmanager.h"
#include "appsettings.h"
#include "workspace.h"

WidgetSytem1::WidgetSytem1(QWidget *parent, const QString &name)
    : QWidget(parent), AppSettingsObserver(name)
    , ui(new Ui::WidgetSytem1)
{
    ui->setupUi(this);
    setStyleSheet("color:white");
    AppSettingsSubject::instance().addObserver(this);

    ui->comboBox1->addItem("English", "English");
    ui->comboBox1->addItem("简体中文", "Chinese");
    connect(ui->comboBox1, &QComboBox::activated,this, [this](int index) {
        AppSettingsChanged(objectName(), "Language", ui->comboBox1->currentData().toString()); 
        QSettings().setValue("Language", ui->comboBox1->currentData().toString());
        AppSettingsSubject::instance().changeLanguage(ui->comboBox1->currentData().toString());
    });

    auto getRatio = [](const QString& text){
        float value = (text.left(text.indexOf("%")).toInt() + 25) / 100.0;
        return QString::number(value);
    };
    int min = 75, max = 200, step = 25, size = (max - min) / step + 1;
    QVector<QString> ratios(size);
    for(int i = 0;i < size; i++){
        ratios[i] = QString::number(min + step * i) + "%";
    }
    for(const auto& ratio : ratios) {
        ui->comboBox2->addItem(ratio, getRatio(ratio));
    }

    connect(ui->comboBox2, &QComboBox::activated,this, [this](int index) {
        APPSHandle.changeScaleFactor(ui->comboBox2->currentData().toString());
        emit attributeChanged(objectName(), "InterfaceScale", ui->comboBox2->currentData().toString());
    });
    connect(ui->button3, &QPushButton::clicked, this, [this](bool checked) { 
        QSettings().setValue("FollowSystemScale", checked);
        emit attributeChanged(objectName(), "FollowSystem", QString::number(checked));
    });
    connect(ui->button4, &QPushButton::clicked, this, [this](bool checked) { 
        QSettings().setValue("AutoSaveWorkspace", checked);
        WKSPHandle.modifyWorkspacePreprocessing(checked?WorkspaceSubject::onOrigin:WorkspaceSubject::onCopy);
        emit attributeChanged(objectName(), "AutoSaveWorkspace", QString::number(checked)); 
    });
    connect(ui->button5, &QPushButton::clicked, this, [this](bool checked) { 
        QSettings().setValue("AutoStartOnBoot", checked);
        ATSMHandle.setAutoStart(checked);
        emit attributeChanged(objectName(), "AutoStart", QString::number(checked)); 
    });
    connect(ui->button6, &QPushButton::clicked, this, [this](bool checked) {
        QSettings().setValue("AutoCheckForUpdates", checked);
        emit attributeChanged(objectName(), "AutoCheckUpdate", QString::number(checked));
    });
}

WidgetSytem1::~WidgetSytem1()
{
    delete ui;
}

void WidgetSytem1::setName(const QString &name)
{
    setObjectName(name);
    AppSettingsObserver::setObserverName(name);
}

void WidgetSytem1::setFont(const QFont &font)
{
    m_font = font;
}

void WidgetSytem1::changeLanguage(QString language)
{
    if (language == "Chinese") {
        ui->label1->setText("语言选择");        
        ui->label2->setText("界面比例");
        ui->label3->setText("界面比例跟随系统");
        ui->label4->setText("自动保存工作区");
        ui->label5->setText("开机自启动");
        ui->label6->setText("自动检查更新");
    } else if( language == "English") {
        ui->label1->setText("Language");
        ui->label2->setText("UI Scale");
        ui->label3->setText("UI Scales follow OS display setting");
        ui->label4->setText("Auto save workspace");
        ui->label5->setText("Autorun");
        ui->label6->setText("Auto check for updates");
    }
}

void WidgetSytem1::setLanguage(const QString &language)
{
    int index = ui->comboBox1->findData(language);
    if (index != -1)
    {
        ui->comboBox1->setCurrentIndex(index);
    }
}

void WidgetSytem1::setInterfaceScale(float scale)
{
    QString interfaceScale = QString::number(scale);
    int index = ui->comboBox2->findData(interfaceScale);
    if (index != -1)
    {
        ui->comboBox2->setCurrentIndex(index);
    }
}

void WidgetSytem1::setFollowSystem(bool follow)
{
    ui->button3->setChecked(follow);
}

void WidgetSytem1::setAutoSaveWorkspace(bool autoSave)
{
    ui->button4->setChecked(autoSave);
}

void WidgetSytem1::setAutoStart(bool autoStart)
{
    ui->button5->setChecked(autoStart);
}

void WidgetSytem1::setAutoStartByRegedit(bool autoStart)
{
    setAutoStart(autoStart);
    ATSMHandle.setAutoStart(autoStart);
}

void WidgetSytem1::setAutoCheckUpdate(bool autoCheck)
{
    ui->button6->setChecked(autoCheck);
}

QString WidgetSytem1::getLanguage() const
{
    return ui->comboBox1->currentData().toString();
}

int WidgetSytem1::getInterfaceScale() const
{
    return ui->comboBox2->currentData().toInt();
}

bool WidgetSytem1::isFollowSystem() const
{
    return ui->button3->isChecked();
}

bool WidgetSytem1::isAutoSaveWorkspace() const
{
    return ui->button4->isChecked();
}

bool WidgetSytem1::isAutoStart() const
{
    return ui->button5->isChecked();
}

bool WidgetSytem1::isAutoCheckUpdate() const
{
    return ui->button6->isChecked();
}

int WidgetSytem1::itemCount()const
{
    int widgetCount = 0;
    for(int i = 0; i < ui->verticalLayout->count(); i++) {
        if( ui->verticalLayout->itemAt(i)->widget() != nullptr) {
            widgetCount++;
        }
    }
    return widgetCount;
}

void WidgetSytem1::resizeEvent(QResizeEvent *event)
{
    int vSpace = 0.12 * ui->widget1->height();
    int iconWH = ui->widget1->height()-2*vSpace;
    int iconY = vSpace;
    int hSpace = 0.02*ui->widget1->width();
    int wButton = 2*ui->widget4->height();
    {
        double wRatio = ui->widget1->width()/100.0;
        int wCombBox = 0.2*ui->widget1->width();
        ui->icon1->setGeometry(0, iconY, iconWH, iconWH);
        ui->label1->setGeometry(ui->icon1->width() + hSpace, iconY, ui->widget1->width() - ui->icon1->width() - 2*hSpace-wCombBox, iconWH);
        ui->comboBox1->setGeometry(ui->widget1->width() - wCombBox, iconY, wCombBox, iconWH);
    }
    {
        double wRatio = ui->widget2->width()/100.0;
        int wCombBox = 0.15*ui->widget2->width();
        ui->icon2->setGeometry(0, iconY, iconWH, iconWH);
        ui->label2->setGeometry(ui->icon2->width() + hSpace, iconY, ui->widget2->width() - ui->icon2->width() - 2*hSpace-wCombBox, iconWH);
        ui->comboBox2->setGeometry(ui->widget2->width() - wCombBox, iconY, wCombBox, iconWH);
    }
    {
        double wRatio = ui->widget3->width()/100.0;
        ui->icon3->setGeometry(0, iconY, iconWH, iconWH);
        ui->label3->setGeometry(ui->icon3->width() + hSpace, iconY, ui->widget3->width() - ui->icon3->width() - 2*hSpace-wButton, iconWH);
        ui->button3->setGeometry(ui->widget3->width() - wButton, 0, wButton, ui->widget3->height());
    }
    {
        double wRatio = ui->widget4->width()/100.0;
        ui->icon4->setGeometry(0, iconY, iconWH, iconWH);
        ui->label4->setGeometry(ui->icon4->width() + hSpace, iconY, ui->widget4->width() - ui->icon4->width() - 2*hSpace-wButton, iconWH);
        ui->button4->setGeometry(ui->widget4->width() - wButton, 0, wButton, ui->widget4->height());
    }
    {
        double wRatio = ui->widget5->width()/100.0;
        ui->icon5->setGeometry(0, iconY, iconWH, iconWH);
        ui->label5->setGeometry(ui->icon5->width() + hSpace, iconY, ui->widget5->width() - ui->icon5->width() - 2*hSpace-wButton, iconWH);
        ui->button5->setGeometry(ui->widget5->width() - wButton, 0, wButton, ui->widget5->height());
    }
    {
        double wRatio = ui->widget6->width()/100.0;
        ui->icon6->setGeometry(0, iconY, iconWH, iconWH);
        ui->label6->setGeometry(ui->icon6->width() + hSpace, iconY, ui->widget6->width() - ui->icon6->width() - 2*hSpace-wButton, iconWH);
        ui->button6->setGeometry(ui->widget6->width() - wButton, 0, wButton, ui->widget6->height());
    }

    m_font.setPointSizeF(GLBFHandle.getSuitablePointSize(m_font, height()*0.053));
    ui->label1->setFont(m_font);
    ui->label2->setFont(m_font);
    ui->label3->setFont(m_font);
    ui->label4->setFont(m_font);
    ui->label5->setFont(m_font);
    ui->label6->setFont(m_font);
    ui->comboBox1->setFont(m_font);
    ui->comboBox2->setFont(m_font);
}

void WidgetSytem1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    if (attribute == "Language") {
        changeLanguage(value);
    }
}