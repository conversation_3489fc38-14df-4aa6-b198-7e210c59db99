{"backtrace": 3, "backtraceGraph": {"commands": ["add_custom_target", "qt6_add_ui", "qt_add_ui"], "files": ["D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsMacros.cmake", "Source/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 2, "file": 1, "line": 90, "parent": 0}, {"command": 1, "file": 0, "line": 340, "parent": 1}, {"command": 0, "file": 0, "line": 321, "parent": 2}]}, "folder": {"name": "QtInternalTargets"}, "id": "TPCC_ui_property_check::@43690dd2fb94c8e45e84", "name": "TPCC_ui_property_check", "paths": {"build": "Source", "source": "Source"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 3, "isGenerated": true, "path": "build/Source/CMakeFiles/TPCC_ui_property_check", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Source/CMakeFiles/TPCC_ui_property_check.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Source/TPCC_autogen/ui_property_check_timestamp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}