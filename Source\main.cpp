// #include <QDir>
// #include <QApplication>
// #include <QStyleFactory>

// #include "usbhidapi.h"
// #include "globalfont.h"
// #include "appsettings.h"
// #include "usbaudioapi.h"
// #include "debugmanager.h"
// #include "trialmanager.h"
// #include "deviceconnector.h"
// #include "usbaudiomanager.h"
// #include "singleinstancemanager.h"


// int main(int argc, char *argv[])
// {
//     QApplication a(argc, argv);
// #if defined(Q_OS_MACOS)
//     QApplication::setQuitOnLastWindowClosed(false);
// #endif
// #if defined(Q_OS_WIN)
//     a.setStyle(QStyleFactory::create("windowsvista"));
// #endif
//     QDir::setCurrent(QCoreApplication::applicationDirPath());
//     DBGMHandle.installMessageHandler();
//     QStringList fontList;
//     fontList << ":/Font/SourceHanSansCN-Bold.ttf";
//     fontList << ":/Font/NotoSans-Bold.ttf";
//     fontList << ":/Font/NotoSans-ExtraBold.ttf";
//     fontList << ":/Font/NotoSans-ExtraLight.ttf";
//     fontList << ":/Font/NotoSans-Light.ttf";
//     fontList << ":/Font/NotoSans-Medium.ttf";
//     fontList << ":/Font/NotoSans-Regular.ttf";
//     GLBFHandle.load(fontList);
//     // qApp->setFont(GLBFHandle.font()); // 可使用此方式设置全局的字体，已验证，真实有效
//     SGIMHandle.check(); // 检查软件是否多开
//     TRLMHandle.check(TrialManager::BuildTime, -1); // 检查软件是否试用截止
//     APPSHandle.init();
//     USBAHandle.init();
//     USAMHandle.init();
//     USBHHandle.init();
//     // USBAHandle.showPropertiesOfAllDevice();

//     DeviceConnector deviceConnector;
//     QHash<QString, QPair<QString, QString>> deviceList;
//     deviceList.insert("M62", {"0x152A", "0x875C"});
//     deviceConnector.showSeries("Series-M", deviceList);

//     int ret=a.exec();
//     USBHHandle.exit();
//     return ret;
// }

