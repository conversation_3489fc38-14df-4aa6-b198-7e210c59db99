#include "eqwidget.h"
#include <QScrollBar>
#include <QTimer>
#include <QDebug>
#include <QLabel>
#include <QPushButton>
#include <QVector>
#include <QScrollArea>
#include <QShowEvent>
#include <QResizeEvent>
#include "globalfont.h"
#include "comboboxs1m3.h"
#include "eqwidgetiem.h"

EqWidget::EqWidget(QWidget* parent)
    : QWidget(parent)
    , mSizeFactor(1.0)
    , mUpdateSize(false)
    , mScrollArea(nullptr)
    , mScrollWidget(nullptr)
    , mTitleTypeLabel(nullptr)
    , mTitleGainLabel(nullptr)
    , mTitleFrequencyLabel(nullptr)
    , mTitleQLabel(nullptr)
    , mAddItemButton(nullptr)
    , mRemoveItemButton(nullptr)
{
    setupUI();
    createDefaultItems();
    setStyleSheet(
        "QWidget {"
        "    background-color: rgb(22,22,22);"
        "}"
        "QLabel {"
        "    color: #404040;"
        "}"
        "QPushButton {"
        "    background-color: #43cf7c;"
        "    color: #ffffff;"
        "}"
        "QPushButton:hover {"
        "    background-color: #52d689;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #3bb56e;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #666666;"
        "    color: #999999;"
        "}"
    );
    mScrollArea->setStyleSheet(
        "QScrollArea {"
        "   background-color: transparent;"
        "}"
        "QScrollBar::sub-line:horizontal {"
        "   width: 0px;"
        "}"
        "QScrollBar::add-line:horizontal {"
        "   width: 0px;"
        "}"
        "QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {"
        "background: none;""}"
    );
    setSizeFactor(mSizeFactor);
}

EqWidget::~EqWidget()
{

}

EqWidget& EqWidget::setName(QString name)
{
    setObjectName(name);
    return *this;
}
EqWidget& EqWidget::setFont(QFont font)
{
    mFont = font;
    resizeEvent(nullptr);
    return *this;
}

void EqWidget::setupUI()
{
    setMinimumSize(600,300);

    mScrollArea = new QScrollArea(this);
    mScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    mScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    mScrollArea->setWidgetResizable(true);

    mScrollWidget = new QWidget();
    mScrollArea->setWidget(mScrollWidget);

    mTitleTypeLabel = new QLabel("Type", this);
    mTitleTypeLabel->setAlignment(Qt::AlignCenter);
    mTitleGainLabel = new QLabel("Gain", this);
    mTitleGainLabel->setAlignment(Qt::AlignCenter);
    mTitleFrequencyLabel = new QLabel("Freq", this);
    mTitleFrequencyLabel->setAlignment(Qt::AlignCenter);
    mTitleQLabel = new QLabel("Q", this);
    mTitleQLabel->setAlignment(Qt::AlignCenter);

    mAddItemButton = new QPushButton("add", this);
    connect(mAddItemButton, &QPushButton::clicked, this, [this](){
        addItem();
    });

    mRemoveItemButton = new QPushButton("remove", this);
    connect(mRemoveItemButton, &QPushButton::clicked, this, [this](){
        removeItem(-1);
    });

    auto comboBox = new ComboBoxS1M3(this);
    comboBox->addItems({"1","1.5", "2", "2.5", "3"});
    connect(comboBox, &ComboBoxS1M3::currentIndexChanged, this, [comboBox,this](int index) {
        setSizeFactor(comboBox->itemText(index).toDouble());
    });
}

void EqWidget::adjustFontAndSize()
{
    if (!mScrollArea || !mTitleTypeLabel) return;
    
    int w = width();
    int h = height();
    int titleWidth = w * 0.08;
    int scrollAreaX = titleWidth + 10;
    int scrollAreaWidth = w - scrollAreaX - 10;

    int titleY = h * 0.1;
    int titleHeight = h * 0.08;
    int titleSpacing = h * 0.15;

    mTitleTypeLabel->setGeometry(0, titleY, titleWidth, titleHeight);
    titleY += titleSpacing;

    mTitleGainLabel->setGeometry(0, titleY, titleWidth, titleHeight);
    titleY += titleSpacing;

    mTitleFrequencyLabel->setGeometry(0, titleY, titleWidth, titleHeight);
    titleY += titleSpacing;

    mTitleQLabel->setGeometry(0, titleY, titleWidth, titleHeight);

    mScrollArea->setGeometry(scrollAreaX, 0, scrollAreaWidth, h - 40);

    mAddItemButton->setGeometry(0, 0, 80, 30);
    mRemoveItemButton->setGeometry(85, 0, 80, 30);

    float hPixelPerRatio = height() / 100.0;
    int marginLeft = hPixelPerRatio * 2;
    int marginBottom = hPixelPerRatio * 3;

    QString style;
    style += QString("QScrollBar::handle:horizontal {"
                        "   background: #43CF7C;"
                        "   min-width: 20px;"
                        "   border-radius: %1px;"
                        "}"
                        "QScrollBar::handle:horizontal:hover {"
                        "   background: #43CF7C;"
                        "}"
                        "QScrollBar::handle:horizontal:pressed {"
                        "   background: #43CF7C;"
                        "}").arg(marginBottom * 0.25);
    style += QString("QScrollBar:horizontal {"
                     "   background: #2B2B2B;"
                     "   height: %1px;"
                     "   border-radius: %2px;"
                     "   margin-top: %3px;"
                     "   margin-bottom: %3px;"
                     "   margin-left: %3px;"
                     "   margin-right: %3px;"
                     "}").arg(marginBottom * 2).arg(marginBottom * 0.25).arg(1.1*marginLeft);
    mScrollArea->horizontalScrollBar()->setStyleSheet(style);

    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height()*0.05));
    mTitleTypeLabel->setFont(mFont);
    mTitleGainLabel->setFont(mFont);
    mTitleFrequencyLabel->setFont(mFont);
    mTitleQLabel->setFont(mFont);
}

void EqWidget::ajudstMargin()
{
    int h = height();
    double margin = h/5.0;
    mTitleTypeLabel->move(0, margin);
    mTitleGainLabel->move(0, mTitleTypeLabel->rect().bottom() + margin);
    mTitleFrequencyLabel->move(0, mTitleGainLabel->rect().bottom() + margin);
    mTitleQLabel->move(0, mTitleFrequencyLabel->rect().bottom() + margin);
}

void EqWidget::updateItemSize()
{
    if (!mScrollWidget || mItems.isEmpty()) return;

    int itemWidth = 100 * mSizeFactor;
    int itemSpacing = 10;
    int totalWidth = mItems.size() * itemWidth + (mItems.size() - 1) * itemSpacing;

    mScrollWidget->setFixedSize(totalWidth, mScrollArea->height());

    int currentX = 0;
    for (int i = 0; i < mItems.size(); ++i) {
        EqWidgetIem* item = mItems[i];
        item->setGeometry(currentX, 0, itemWidth, mScrollArea->height());
        currentX += itemWidth + itemSpacing;
    }
}

void EqWidget::createDefaultItems()
{
    setItemCount(4);
}

void EqWidget::setSizeFactor(double sizeFactor)
{
    mSizeFactor = sizeFactor;
    mUpdateSize = true;
    resize(minimumWidth() * sizeFactor, minimumHeight() * sizeFactor);
    updateItemSize();
    setItemStretch(mSizeFactor);
}

void EqWidget::setItemStretch(double sizeFactor)
{
    for (auto item : mItems) {
        item->setSizeFactor(sizeFactor);
    }
}

void EqWidget::setItemCount(int count)
{
    if (count < 0) count = 0;
    if (count > 32) count = 32;

    int currentCount = mItems.size();

    if (count > currentCount) {
        for (int i = currentCount; i < count; ++i) {
            addItem();
        }
    } else if (count < currentCount) {
        for (int i = currentCount - 1; i >= count; --i) {
            removeItem(i);
        }
    }

    emit ItemCountChanged(count);
}

void EqWidget::addItem()
{
    setUpdatesEnabled(false);

    int index = mItems.size();
    EqWidgetIem* ItemWidget = new EqWidgetIem(index, mScrollWidget);
    ItemWidget->setFont(mFont);
    ItemWidget->setSizeFactor(mSizeFactor);

    connect(ItemWidget, &EqWidgetIem::dataChanged,this, &EqWidget::itemDataChanged);
    connect(ItemWidget, &EqWidgetIem::dataChanged,this, [this](int index, const EqWidgetItemData& data) {
        auto changedAttributes = data.getChangedAttributes();
        for (const auto& attr : changedAttributes) {
            emit attributeChanged(objectName(), attr.first, attr.second);
        }
    });

    mItems.append(ItemWidget);

    updateItemSize();

    ItemWidget->show();
    setUpdatesEnabled(true);

    mRemoveItemButton->setEnabled(mItems.size() > 1);

    emit ItemCountChanged(mItems.size());
}

void EqWidget::removeItem(int index)
{
    if (mItems.isEmpty()) return;

    if (index < 0 || index >= mItems.size()) {
        index = mItems.size() - 1;
    }

    EqWidgetIem* ItemWidget = mItems.takeAt(index);
    ItemWidget->deleteLater();

    updateItemIndices();
    updateItemSize();

    mRemoveItemButton->setEnabled(mItems.size() > 1);

    emit ItemCountChanged(mItems.size());
}

void EqWidget::removeAllItems()
{
    while (!mItems.isEmpty()) {
        removeItem(-1);
    }
}

void EqWidget::updateItemIndices()
{
    for (int i = 0; i < mItems.size(); ++i) {
        mItems[i]->setItemIndex(i);
    }
}

void EqWidget::setEqData(const QVector<EqWidgetItemData>& data)
{
    setItemCount(data.size());

    for (int i = 0; i < data.size() && i < mItems.size(); ++i) {
        mItems[i]->setItemData(data[i]);
    }
}

QVector<EqWidgetItemData> EqWidget::getEqData() const
{
    QVector<EqWidgetItemData> data;
    for (const auto& Item : mItems) {
        data.append(Item->getItemData());
    }
    return data;
}

void EqWidget::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);

    if (!mScrollArea || !mTitleTypeLabel || !mTitleGainLabel ||
        !mTitleFrequencyLabel || !mTitleQLabel || !mAddItemButton ||
        !mRemoveItemButton) {
        return;
    }

    if(mUpdateSize){
        mUpdateSize = false;
        adjustFontAndSize();
    }else{
        ajudstMargin();
    }
}
