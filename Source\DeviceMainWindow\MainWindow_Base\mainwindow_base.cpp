#include "appsettings.h"
#include "usbaudioapi.h"
#include "mainwindow_base.h"
#include "usbaudiomanager.h"


MainWindow_Base::MainWindow_Base(QWidget* parent)
    : QMainWindow(parent)
{
    mTimerFrameRate.setInterval(1000);
    mTimerAuth.setInterval(500);
    mTimerAuth.setInterval(500);
    connect(&mTimerFrameRate, &QTimer::timeout, this, &MainWindow_Base::in_mTimerFrameRate_timeout, Qt::UniqueConnection);
    connect(&mTimerAuth, &QTimer::timeout, this, &MainWindow_Base::in_mTimerAuth_timeout, Qt::UniqueConnection);
    connect(&mTimerOnline, &QTimer::timeout, this, &MainWindow_Base::in_mTimerOnline_timeout, Qt::UniqueConnection);
    connect(&USAMHandle, &USBAudioManager::attributeChanged, this, &MainWindow_Base::in_USBAudio_attributeChanged, Qt::UniqueConnection);
}
MainWindow_Base::~MainWindow_Base()
{
    APPSHandle.removeObserverAll();
    USAMHandle.stop();
}


// slot
void MainWindow_Base::in_mTimerFrameRate_timeout()
{
    qInfo() << "FPS: " << mFrameRate;
    mFrameRate = 0;
}
void MainWindow_Base::in_mTimerAuth_timeout()
{
    mAuthCounter++;
    if(mAuthCounter == 10)
    {
        setAuthResult(0);
        return;
    }
    sendAuthInfoToDevice();
}
void MainWindow_Base::in_mTimerOnline_timeout()
{
    if(!USBAHandle.isDeviceOnline())
    {
        in_mDevice_deviceDisconnected();
    }
}
void MainWindow_Base::in_USBAudio_attributeChanged(QString attribute, QString value)
{
    onUSBAudioAttributeChanged(attribute, value);
}
void MainWindow_Base::in_mDevice_deviceDisconnected()
{
    emit attributeChanged("", "Disconnected", "1");
}


// setter & getter
void MainWindow_Base::showFrameRate()
{
    mTimerFrameRate.start();
}
void MainWindow_Base::increaseFrameTick()
{
    if(mTimerFrameRate.isActive())
    {
        mFrameRate++;
    }
}
void MainWindow_Base::assignDevice(DeviceBase* device, QString deviceName)
{
    mDevice = device;
    mDeviceName = deviceName;
    connect(mDevice, &DeviceBase::deviceDisconnected, this, &MainWindow_Base::in_mDevice_deviceDisconnected, Qt::UniqueConnection);
}
void MainWindow_Base::startDeviceAuthentication()
{
    if(!mDevice->open())
    {
        setAuthResult(-1);
        mTimerOnline.start();
        return;
    }
    mDevice->start();
    sendAuthInfoToDevice();
    mTimerAuth.start();
}
void MainWindow_Base::setAuthResult(int result)
{
    mTimerAuth.stop();
    mAuthCounter = 0;
    onAuthResult(result);
    if(result)
    {
        APPSHandle.changeLanguage(QSettings().value("Language").toString()).assignMainWindow(this);
        USBAHandle.setDeviceToActiveByName(mDeviceName);
        if(result != -1) USAMHandle.start();
        show();
        emit attributeChanged("", "AuthResult", "Success");
    }
    else
    {
        emit attributeChanged("", "AuthResult", "Failed");
    }
}

