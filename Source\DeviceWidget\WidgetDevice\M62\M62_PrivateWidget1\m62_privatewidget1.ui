<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>M62_PrivateWidget1</class>
 <widget class="QWidget" name="M62_PrivateWidget1">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1435</width>
    <height>686</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="18,200,18">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>65</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="28,236,28,394,28,236,28">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="widget_2" native="true">
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <widget class="M62_PrivateWidget1_1" name="input" native="true">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>70</y>
          <width>81</width>
          <height>181</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>32</width>
          <height>65</height>
         </size>
        </property>
       </widget>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_4">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="widget_3" native="true">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <widget class="QWidget" name="widgetDials" native="true">
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>301</width>
          <height>331</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>53</width>
          <height>65</height>
         </size>
        </property>
        <widget class="QWidget" name="widgetD" native="true">
         <property name="geometry">
          <rect>
           <x>30</x>
           <y>20</y>
           <width>261</width>
           <height>231</height>
          </rect>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <widget class="QWidget" name="widgetThreshold" native="true">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>167</width>
            <height>157</height>
           </rect>
          </property>
          <property name="minimumSize">
           <size>
            <width>50</width>
            <height>50</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <widget class="QLabel" name="DialThresholdT">
           <property name="geometry">
            <rect>
             <x>40</x>
             <y>0</y>
             <width>58</width>
             <height>16</height>
            </rect>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>Threshold</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
           </property>
          </widget>
          <widget class="DialS1M5" name="DialThreshold" native="true">
           <property name="geometry">
            <rect>
             <x>20</x>
             <y>30</y>
             <width>91</width>
             <height>81</height>
            </rect>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
          </widget>
          <widget class="QLabel" name="DialThresholdL">
           <property name="geometry">
            <rect>
             <x>30</x>
             <y>130</y>
             <width>23</width>
             <height>16</height>
            </rect>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>0dB</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
          <widget class="QLabel" name="DialThresholdR">
           <property name="geometry">
            <rect>
             <x>110</x>
             <y>130</y>
             <width>16</width>
             <height>16</height>
            </rect>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>-∞</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </widget>
         <widget class="QWidget" name="widgetlAttack" native="true">
          <property name="geometry">
           <rect>
            <x>167</x>
            <y>0</y>
            <width>167</width>
            <height>157</height>
           </rect>
          </property>
          <property name="minimumSize">
           <size>
            <width>50</width>
            <height>50</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <widget class="QLabel" name="DialAttackT">
           <property name="geometry">
            <rect>
             <x>50</x>
             <y>10</y>
             <width>53</width>
             <height>21</height>
            </rect>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>Attack</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
           </property>
          </widget>
          <widget class="DialS1M5" name="DialAttack" native="true">
           <property name="geometry">
            <rect>
             <x>40</x>
             <y>40</y>
             <width>61</width>
             <height>51</height>
            </rect>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
          </widget>
          <widget class="QLabel" name="DialAttackL">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>110</y>
             <width>53</width>
             <height>21</height>
            </rect>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>Fast</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
          <widget class="QLabel" name="DialAttackR">
           <property name="geometry">
            <rect>
             <x>120</x>
             <y>130</y>
             <width>53</width>
             <height>21</height>
            </rect>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>Slow</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </widget>
         <widget class="QWidget" name="widgeReduction" native="true">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>157</y>
            <width>167</width>
            <height>156</height>
           </rect>
          </property>
          <property name="minimumSize">
           <size>
            <width>50</width>
            <height>50</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <widget class="QLabel" name="DialReductionT">
           <property name="geometry">
            <rect>
             <x>50</x>
             <y>20</y>
             <width>61</width>
             <height>21</height>
            </rect>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>Reduction</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
           </property>
          </widget>
          <widget class="DialS1M5" name="DialReduction" native="true">
           <property name="geometry">
            <rect>
             <x>50</x>
             <y>50</y>
             <width>61</width>
             <height>61</height>
            </rect>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
          </widget>
          <widget class="QLabel" name="DialReductionL">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>120</y>
             <width>53</width>
             <height>21</height>
            </rect>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>0dB</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
          <widget class="QLabel" name="DialReductionR">
           <property name="geometry">
            <rect>
             <x>100</x>
             <y>120</y>
             <width>53</width>
             <height>21</height>
            </rect>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>-∞</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </widget>
         <widget class="QWidget" name="widgetRelease" native="true">
          <property name="geometry">
           <rect>
            <x>167</x>
            <y>157</y>
            <width>167</width>
            <height>156</height>
           </rect>
          </property>
          <property name="minimumSize">
           <size>
            <width>50</width>
            <height>50</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <widget class="QLabel" name="DialReleaseT">
           <property name="geometry">
            <rect>
             <x>40</x>
             <y>20</y>
             <width>53</width>
             <height>21</height>
            </rect>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>Release</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
           </property>
          </widget>
          <widget class="DialS1M5" name="DialRelease" native="true">
           <property name="geometry">
            <rect>
             <x>50</x>
             <y>50</y>
             <width>71</width>
             <height>61</height>
            </rect>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
          </widget>
          <widget class="QLabel" name="DialReleaseL">
           <property name="geometry">
            <rect>
             <x>20</x>
             <y>120</y>
             <width>53</width>
             <height>21</height>
            </rect>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>Fast</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
          <widget class="QLabel" name="DialReleaseR">
           <property name="geometry">
            <rect>
             <x>110</x>
             <y>120</y>
             <width>53</width>
             <height>21</height>
            </rect>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>Slow</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </widget>
        </widget>
        <widget class="QPushButton" name="buttonOFF">
         <property name="geometry">
          <rect>
           <x>160</x>
           <y>270</y>
           <width>75</width>
           <height>51</height>
          </rect>
         </property>
         <property name="sizePolicy">
          <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>15</height>
          </size>
         </property>
         <property name="text">
          <string>OFF</string>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
        </widget>
       </widget>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_5">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="widget_4" native="true">
       <widget class="QWidget" name="widgetDockingMap" native="true">
        <property name="geometry">
         <rect>
          <x>30</x>
          <y>10</y>
          <width>221</width>
          <height>351</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>32</width>
          <height>65</height>
         </size>
        </property>
        <widget class="QLabel" name="labelTitle_2">
         <property name="geometry">
          <rect>
           <x>40</x>
           <y>10</y>
           <width>79</width>
           <height>16</height>
          </rect>
         </property>
         <property name="text">
          <string>Ducking Map</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QWidget" name="widget" native="true">
         <property name="geometry">
          <rect>
           <x>10</x>
           <y>40</y>
           <width>151</width>
           <height>261</height>
          </rect>
         </property>
         <widget class="QPushButton" name="IN1BT">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>65</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QLabel" name="labelAUX">
          <property name="geometry">
           <rect>
            <x>96</x>
            <y>35</y>
            <width>25</width>
            <height>16</height>
           </rect>
          </property>
          <property name="text">
           <string>AUX</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
         <widget class="QPushButton" name="IN2AUX">
          <property name="geometry">
           <rect>
            <x>48</x>
            <y>35</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QLabel" name="labelpb56">
          <property name="geometry">
           <rect>
            <x>96</x>
            <y>185</y>
            <width>74</width>
            <height>16</height>
           </rect>
          </property>
          <property name="text">
           <string>Playback 5/6</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
         <widget class="QLabel" name="labelBT">
          <property name="geometry">
           <rect>
            <x>96</x>
            <y>65</y>
            <width>16</width>
            <height>16</height>
           </rect>
          </property>
          <property name="text">
           <string>BT</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
         <widget class="QPushButton" name="playback7_8IN2">
          <property name="geometry">
           <rect>
            <x>48</x>
            <y>215</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QPushButton" name="playback3_4IN1">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>155</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QPushButton" name="IN1AUX">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>35</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QPushButton" name="IN1OTG">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>95</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QLabel" name="labelpb34">
          <property name="geometry">
           <rect>
            <x>96</x>
            <y>155</y>
            <width>74</width>
            <height>16</height>
           </rect>
          </property>
          <property name="text">
           <string>Playback 3/4</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
         <widget class="QPushButton" name="playback1_2IN2">
          <property name="geometry">
           <rect>
            <x>48</x>
            <y>125</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QPushButton" name="IN2BT">
          <property name="geometry">
           <rect>
            <x>48</x>
            <y>65</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QPushButton" name="playback5_6IN2">
          <property name="geometry">
           <rect>
            <x>48</x>
            <y>185</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QLabel" name="labelOTG">
          <property name="geometry">
           <rect>
            <x>96</x>
            <y>95</y>
            <width>43</width>
            <height>16</height>
           </rect>
          </property>
          <property name="text">
           <string>OTG IN</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
         <widget class="QPushButton" name="playback5_6IN1">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>185</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QPushButton" name="playback3_4IN2">
          <property name="geometry">
           <rect>
            <x>48</x>
            <y>155</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QLabel" name="labelIN1">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>21</width>
            <height>16</height>
           </rect>
          </property>
          <property name="text">
           <string>IN1</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignBottom|Qt::AlignmentFlag::AlignHCenter</set>
          </property>
         </widget>
         <widget class="QLabel" name="labelpb12">
          <property name="geometry">
           <rect>
            <x>96</x>
            <y>125</y>
            <width>74</width>
            <height>16</height>
           </rect>
          </property>
          <property name="text">
           <string>Playback 1/2</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
         <widget class="QPushButton" name="playback7_8IN1">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>215</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QPushButton" name="IN2OTG">
          <property name="geometry">
           <rect>
            <x>48</x>
            <y>95</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QLabel" name="labelIN2">
          <property name="geometry">
           <rect>
            <x>48</x>
            <y>0</y>
            <width>21</width>
            <height>16</height>
           </rect>
          </property>
          <property name="text">
           <string>IN2</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignBottom|Qt::AlignmentFlag::AlignHCenter</set>
          </property>
         </widget>
         <widget class="QPushButton" name="playback1_2IN1">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>125</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QLabel" name="labelpb78">
          <property name="geometry">
           <rect>
            <x>96</x>
            <y>215</y>
            <width>74</width>
            <height>16</height>
           </rect>
          </property>
          <property name="text">
           <string>Playback 7/8</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
         <widget class="QLabel" name="labelpb910">
          <property name="geometry">
           <rect>
            <x>100</x>
            <y>240</y>
            <width>74</width>
            <height>16</height>
           </rect>
          </property>
          <property name="text">
           <string>Playback 9/10</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
         <widget class="QPushButton" name="playback9_10IN2">
          <property name="geometry">
           <rect>
            <x>52</x>
            <y>240</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QPushButton" name="playback9_10IN1">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>240</y>
            <width>30</width>
            <height>16</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
        </widget>
       </widget>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_6">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer_2">
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>65</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>M62_PrivateWidget1_1</class>
   <extends>QWidget</extends>
   <header location="global">m62_privatewidget1_1.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>DialS1M5</class>
   <extends>QWidget</extends>
   <header location="global">dials1m5.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
