#ifndef WIDGETSYTEM1_H
#define WIDGETSYTEM1_H

#include <QWidget>
#include "appsettings.h"

namespace Ui {
class WidgetSytem1;
}

class WidgetSytem1 : public QWidget, public AppSettingsObserver
{
    Q_OBJECT

public:
    explicit WidgetSytem1(QWidget *parent = nullptr, const QString &name = "");
    ~WidgetSytem1();
    void setName(const QString &name);
    void setFont(const QFont& font);
    void changeLanguage(QString language);
    void setLanguage(const QString &language);
    void setInterfaceScale(float scale);
    void setFollowSystem(bool follow);
    void setAutoSaveWorkspace(bool autoSave);
    void setAutoStart(bool autoStart);
    void setAutoStartByRegedit(bool autoStart);
    void setAutoCheckUpdate(bool autoCheck);

    QString getLanguage() const;
    int getInterfaceScale() const;
    bool isFollowSystem() const;
    bool isAutoSaveWorkspace() const;
    bool isAutoStart() const;
    bool isAutoCheckUpdate() const;
    int itemCount()const;

protected:
    void resizeEvent(QResizeEvent *event) override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
    
signals:
    void attributeChanged(const QString &objectName, const QString &attribute, const QString &value);

private:
    Ui::WidgetSytem1 *ui;
    QFont m_font;
};

#endif // WIDGETSYTEM1_H
