#include "comboboxs1m3.h"
#include <QStyledItemDelegate>
#include <QLineEdit>
#include <QAbstractItemView>
#include <QApplication>
#include <QWheelEvent>

class RightAlignDelegate : public QStyledItemDelegate {
public:
    RightAlignDelegate(QObject *parent = nullptr) : QStyledItemDelegate(parent) {}
    void initStyleOption(QStyleOptionViewItem *option, const QModelIndex &index) const override {
        QStyledItemDelegate::initStyleOption(option, index);
        option->displayAlignment = Qt::AlignCenter;
    }
};

ComboBoxS1M3::ComboBoxS1M3(QWidget* parent)
    : QComboBox(parent)
{
    setItemDelegate(new RightAlignDelegate(this));
    setEditable(true);
    if (QLineEdit *edit = lineEdit()) {
        edit->setReadOnly(true);
        edit->setDisabled(true);
        edit->setStyleSheet("background: transparent; border: none; color: white;");
        edit->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
        edit->installEventFilter(this);
    }
    m_container = view()->parentWidget();
    m_container->installEventFilter(this);
    setAttribute(Qt::WA_NoMousePropagation);
}

void ComboBoxS1M3::setFont(const QFont& font) {
    QComboBox::setFont(font);
    if (QLineEdit *edit = lineEdit()) {
        edit->setFont(font);
    }
}

void ComboBoxS1M3::setIndicatorWHRatio(double wh) {
    m_indicatorWH = wh;
    resizeEvent(nullptr);
}

bool ComboBoxS1M3::eventFilter(QObject *watched, QEvent *event) {
    if (watched == lineEdit() && event->type() == QEvent::MouseButtonPress) {
        showPopup();
        return true;
    }else if(watched == m_container && event->type() == QEvent::MouseButtonPress){
        m_container->setAttribute(Qt::WA_NoMouseReplay);
    }else if(watched == m_container && event->type() == QEvent::MouseButtonRelease){
        return true;
    }
    return QWidget::eventFilter(watched, event);
}

void ComboBoxS1M3::mousePressEvent(QMouseEvent* event){
    showPopup();
}

void ComboBoxS1M3::resizeEvent(QResizeEvent *event) {
    QComboBox::resizeEvent(event);
    QString style = QString(R"(
        QComboBox {
            border: none;
            background-color: transparent;
            color: white;
        }

        QComboBox::drop-down {
            border: none;
            subcontrol-origin: padding;
            subcontrol-position: top right;
            width: %1px;
        }

        QComboBox::down-arrow {
            image: url(:/Icon/down.svg);
            width: %1px;
            height: %1px;
        }
            
        QComboBox QScrollBar:vertical {
           background-color: #2e2e2e;
           border-radius: 0px;
           padding-top: 3px;
           padding-bottom: 3px;
           width: %2px;
        }
        QComboBox QScrollBar::handle:vertical {
           width: %2px;
           border-radius: 3px;
           background-color: rgb(112,112,112);
        }
        QComboBox QScrollBar::handle:vertical:hover {
           background-color: rgb(112,112,112);
        }
        QComboBox QScrollBar::add-line:vertical {
           border: none;
        }
        QComboBox QScrollBar::sub-line:vertical {
           border: none;
        }
        QComboBox QScrollBar::add-page:vertical {
           background: none;
        }
        QComboBox QScrollBar::sub-page:vertical {
           background: none;
        }
    )").arg(height()*m_indicatorWH).arg(height()*m_indicatorWH / 3);
#ifdef Q_OS_MACOS
    style += R"(
        QComboBox QAbstractItemView {
            background-color: #2e2e2e;
            color: white;
            border: 1px solid #555;
            border-radius: 5px;
            selection-background-color: rgb(66,66,66);
            outline: 0;
        }
    )";
#else
    style+=R"(
        QComboBox QAbstractItemView {
            background-color: #2e2e2e;
            color: white;
            border: 1px solid #555;
            border-radius: 5px;
            outline: 0;
        }
        QComboBox QAbstractItemView::item:hover {
            background-color: #444;
        }
        QComboBox QAbstractItemView::item:selected {
            background-color: #444;
        }
    )";
#endif
    setStyleSheet(style);
}

void ComboBoxS1M3::wheelEvent(QWheelEvent* event){
    event->ignore();
}

void ComboBoxS1M3::showPopup() {
#ifdef WIN32
    bool oldAnimationEffects = qApp->isEffectEnabled(Qt::UI_AnimateCombo);
    qApp->setEffectEnabled(Qt::UI_AnimateCombo, false);
#endif
    QComboBox::showPopup();
#ifdef WIN32
    qApp->setEffectEnabled(Qt::UI_AnimateCombo, oldAnimationEffects);
#endif
}