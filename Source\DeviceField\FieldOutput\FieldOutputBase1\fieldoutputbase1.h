#ifndef FIELDOUTPUTBASE1_H
#define FIELDOUTPUTBASE1_H


#include <QPair>
#include <QFont>
#include <QRect>
#include <QLabel>
#include <QColor>
#include <QWidget>
#include <QVector>
#include <QString>
#include <QPainter>
#include <QScrollArea>
#include <QPaintEvent>
#include <QPushButton>
#include <QResizeEvent>

#include "outputbase.h"
#include "buttonboxs1m1.h"


class FieldOutputBase1 : public QWidget
{
    Q_OBJECT
public:
    explicit FieldOutputBase1(QWidget* parent=nullptr);
    ~FieldOutputBase1();
    FieldOutputBase1& modifyWidgetList(QVector<OutputBase*> list);
    FieldOutputBase1& setFont(QFont font);
    FieldOutputBase1& setVisibleList(QVector<QString> list);
    FieldOutputBase1& setFieldTitle(QString text);
    FieldOutputBase1& setFieldColor(QColor color);
    FieldOutputBase1& setWidgetAreaColor(QColor color);
    FieldOutputBase1& setWidgetAreaVisible(bool state=true);
    FieldOutputBase1& setFieldHeadAreaStretchFactor(float factor);
    FieldOutputBase1& setAdditionVisible(bool state=true);
    FieldOutputBase1& setAdditionButtonWeight(int weightWidth, int weightHeight);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
private:
    struct MixerWidget
    {
        bool visible;
        OutputBase* widget;
    };
    QFont mFont;
    float mHeadStretchFactor=5.3;
    QColor mColorBG=QColor(31, 31, 31);
    QColor mColorWidgetArea=QColor(128, 128, 128);
    QRect mRectHead;
    QRect mRectBody;
    QRect mRectWidgetArea;
    QLabel mLabelTitle;
    QScrollArea mScrollArea;
    int mScrollBarValue=0;
    QWidget mWidget;
    QVector<MixerWidget*> mWidgetList;
    ButtonBoxS1M1 mWidgetAddition;
    bool mWidgetAreaVisible=true;
    bool mAdditionVisible=true;
    void drawBG(QPainter* painter);
    void drawWidgetArea(QPainter* painter);
private slots:
    void in_mScrollArea_valueChanged(int value);
    void in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value);
    void in_mWidgetAddition_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDOUTPUTBASE1_H

