#ifndef FIELDINPUTBASE1_H
#define FIELDINPUTBASE1_H


#include <QPair>
#include <QFont>
#include <QRect>
#include <QLabel>
#include <QColor>
#include <QWidget>
#include <QVector>
#include <QString>
#include <QPainter>
#include <QScrollArea>
#include <QPaintEvent>
#include <QPushButton>
#include <QResizeEvent>

#include "inputbase.h"
#include "buttonboxs1m1.h"


class FieldInputBase1 : public QWidget
{
    Q_OBJECT
public:
    explicit FieldInputBase1(QWidget* parent=nullptr);
    ~FieldInputBase1();
    FieldInputBase1& modifyWidgetList(QVector<InputBase*> list);
    FieldInputBase1& setFont(QFont font);
    FieldInputBase1& setVisibleList(QVector<QString> list);
    FieldInputBase1& setFieldTitle(QString text);
    FieldInputBase1& setFieldColor(QColor color);
    FieldInputBase1& setWidgetAreaColor(QColor color);
    FieldInputBase1& setWidgetAreaVisible(bool state=true);
    FieldInputBase1& setFieldHeadAreaStretchFactor(float factor);
    FieldInputBase1& setAdditionVisible(bool state=true);
    FieldInputBase1& setAdditionButtonWeight(int weightWidth, int weightHeight);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
private:
    struct MixerWidget
    {
        bool visible;
        InputBase* widget;
    };
    QFont mFont;
    float mHeadStretchFactor=5.3;
    QColor mColorBG=QColor(31, 31, 31);
    QColor mColorWidgetArea=QColor(128, 128, 128);
    QRect mRectHead;
    QRect mRectBody;
    QRect mRectWidgetArea;
    QLabel mLabelTitle;
    QScrollArea mScrollArea;
    int mScrollBarValue=0;
    QWidget mWidget;
    QVector<MixerWidget*> mWidgetList;
    ButtonBoxS1M1 mWidgetAddition;
    bool mWidgetAreaVisible=true;
    bool mAdditionVisible=true;
    void drawBG(QPainter* painter);
    void drawWidgetArea(QPainter* painter);
private slots:
    void in_mScrollArea_valueChanged(int value);
    void in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value);
    void in_mWidgetAddition_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDINPUTBASE1_H

