#ifndef AUTOGAINS1M1_H
#define AUTOGAINS1M1_H
#include <QHash>
#include <set>
#include "framelesswindow.h"
#include "appsettings.h"

class CircleS1M1;
class QStackedWidget;
class QPushButton;
class QLabel;
class AutoGainS1M1_WidgetBase : public QWidget {
    Q_OBJECT
public:
    explicit AutoGainS1M1_WidgetBase(QWidget* parent = nullptr);
    virtual ~AutoGainS1M1_WidgetBase() = default;

protected:
    void resizeEvent(QResizeEvent* e) override;
    virtual void updateControlsGeometry() = 0;

signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};

class AutoGainS1M1_WidgetS1M1 : public AutoGainS1M1_WidgetBase
{
    Q_OBJECT
public:
    explicit AutoGainS1M1_WidgetS1M1(QWidget* parent = nullptr);

    void setCircleText(const QString& text);
    void setMainText(const QString& text);
    void setSubText(const QString& text);
    void setStartButtonText(const QString& text);

protected:
    void updateControlsGeometry() override;
    
private:
    CircleS1M1* mCircleWidget;
    QPushButton* mStartButton;
    QLabel* mMainLabel;
    QLabel* mSubLabel;
};

class AutoGainS1M1_WidgetS1M2 : public AutoGainS1M1_WidgetBase
{
    Q_OBJECT
public:
    explicit AutoGainS1M1_WidgetS1M2(QWidget* parent = nullptr);

    void setCircleText(const QString& text);
    void setMainText(const QString& text);
    void setCancelButtonText(const QString& text);

protected:
    void updateControlsGeometry() override;

private:
    CircleS1M1* mCircleWidget;
    QLabel* mMainLabel;
    QPushButton* mCancelButton;
};

class AutoGainS1M1_WidgetS1M3 : public AutoGainS1M1_WidgetBase
{
    Q_OBJECT
public:
    explicit AutoGainS1M1_WidgetS1M3(QWidget* parent = nullptr);

    void setCircleText(const QString& text);
    void setMainText(const QString& text);
    void setApplyButtonText(const QString& text);
    void setAbandonButtonText(const QString& text);

protected:
    void updateControlsGeometry() override;

private:
    CircleS1M1* mCircleWidget;
    QLabel* mMainLabel;
    QPushButton* mApplyButton;
    QPushButton* mAbandonButton;
};

class AutoGainS1M1_WidgetS1M4 : public AutoGainS1M1_WidgetBase
{
    Q_OBJECT
public:
    explicit AutoGainS1M1_WidgetS1M4(QWidget* parent = nullptr);

    void setCircleText(const QString& text);
    void setMainText(const QString& text);
    void setRetryButtonText(const QString& text);

protected:
    void updateControlsGeometry() override;

private:
    CircleS1M1* mCircleWidget;
    QLabel* mMainLabel;
    QPushButton* mRetryButton;
};

class AutoGainS1M1 : public FramelessWindow, public AppSettingsObserver{
    Q_OBJECT
public:
    enum State {
        invalid = -1,
        Start,
        Detect,
        Complete,
        Error
    };
public:
    explicit AutoGainS1M1(FramelessWindow* parent = nullptr, const QString& name = "");
    ~AutoGainS1M1();

    void setName(const QString& name);
    void setDbRange(float minDb, float maxDb);
    void sampleLevel(float level);
    void setFont(const QFont& font);
    void switchToState(State status);
    int exec()override;
    void changeLanguage(QString language="Chinese");

signals:
    void attributeChanged(QString attribute, QString value);

protected:
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;

private:
    void onTimerTimeout();
    float getFinalSampleLevel();
    template<typename T>
    T* getWidget(State state);
    void handleAttributeChanged(QString objectName, QString attribute, QString value);
    void setAllChildFont(QWidget* widget, const QFont& font);
    void setStartText(const QString& circleText, const QString& mainText,  const QString& subText, const QString& buttonText);
    void setDetectText(const QString& circleText, const QString& mainText,  const QString& cancelText);
    void setCompleteText(const QString& circleText, const QString& mainText,  const QString& applyText, const QString& abandonText);
    void setErrorText(const QString& circleText, const QString& mainText, const QString& retryText);
    void startTimer();
    void stopTimer();

private:
    QStackedWidget* mStackedWidget;
    QHash<State, AutoGainS1M1_WidgetBase*> mHashStateToWidget;
    State mCurrentState;
    QTimer* mTimer;
    int mTimeoutSeconds;
    std::multiset<float, std::greater<float>> mSampleValues;
    float mFinalGainValue;
    float mMinDb;
    float mMaxDb;
    bool mHasCalculated;
};

#endif // AUTOGAINS1M1_H

