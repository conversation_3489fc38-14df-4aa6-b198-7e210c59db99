<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>M62_PrivateWidget3</class>
 <widget class="QWidget" name="M62_PrivateWidget3">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1089</width>
    <height>649</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>313</width>
    <height>220</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QFrame" name="frameMain">
     <property name="frameShape">
      <enum>QFrame::Shape::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="101,80,80,52">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="widget1" native="true">
        <widget class="QSlider" name="sliderInput1">
         <property name="geometry">
          <rect>
           <x>20</x>
           <y>260</y>
           <width>22</width>
           <height>160</height>
          </rect>
         </property>
        </widget>
        <widget class="QLabel" name="labelInput1">
         <property name="geometry">
          <rect>
           <x>10</x>
           <y>220</y>
           <width>53</width>
           <height>15</height>
          </rect>
         </property>
         <property name="text">
          <string>IN1</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelInputLevel1">
         <property name="geometry">
          <rect>
           <x>10</x>
           <y>450</y>
           <width>53</width>
           <height>15</height>
          </rect>
         </property>
         <property name="text">
          <string>+12</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelInput2Level">
         <property name="geometry">
          <rect>
           <x>70</x>
           <y>450</y>
           <width>53</width>
           <height>15</height>
          </rect>
         </property>
         <property name="text">
          <string>-∞</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QSlider" name="sliderInput2">
         <property name="geometry">
          <rect>
           <x>80</x>
           <y>260</y>
           <width>22</width>
           <height>160</height>
          </rect>
         </property>
        </widget>
        <widget class="QLabel" name="labelInput2">
         <property name="geometry">
          <rect>
           <x>70</x>
           <y>220</y>
           <width>53</width>
           <height>15</height>
          </rect>
         </property>
         <property name="text">
          <string>IN2</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelAuxLevel">
         <property name="geometry">
          <rect>
           <x>130</x>
           <y>450</y>
           <width>53</width>
           <height>15</height>
          </rect>
         </property>
         <property name="text">
          <string>-99</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelAux">
         <property name="geometry">
          <rect>
           <x>130</x>
           <y>220</y>
           <width>53</width>
           <height>15</height>
          </rect>
         </property>
         <property name="text">
          <string>AUX</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QSlider" name="sliderAux">
         <property name="geometry">
          <rect>
           <x>140</x>
           <y>260</y>
           <width>22</width>
           <height>160</height>
          </rect>
         </property>
        </widget>
        <widget class="QLabel" name="labelBluetooth">
         <property name="geometry">
          <rect>
           <x>190</x>
           <y>220</y>
           <width>53</width>
           <height>15</height>
          </rect>
         </property>
         <property name="text">
          <string>BT</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QSlider" name="sliderBluetooth">
         <property name="geometry">
          <rect>
           <x>200</x>
           <y>260</y>
           <width>22</width>
           <height>160</height>
          </rect>
         </property>
        </widget>
        <widget class="QLabel" name="labelBluetoothLevel">
         <property name="geometry">
          <rect>
           <x>190</x>
           <y>450</y>
           <width>53</width>
           <height>15</height>
          </rect>
         </property>
         <property name="text">
          <string>-99</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelOtg">
         <property name="geometry">
          <rect>
           <x>240</x>
           <y>220</y>
           <width>53</width>
           <height>15</height>
          </rect>
         </property>
         <property name="text">
          <string>OTG</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QSlider" name="sliderOtg">
         <property name="geometry">
          <rect>
           <x>250</x>
           <y>260</y>
           <width>22</width>
           <height>160</height>
          </rect>
         </property>
        </widget>
        <widget class="QLabel" name="labelOtgLevel">
         <property name="geometry">
          <rect>
           <x>240</x>
           <y>450</y>
           <width>53</width>
           <height>15</height>
          </rect>
         </property>
         <property name="text">
          <string>-99</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget2" native="true">
        <widget class="QLabel" name="labelNoiseClass">
         <property name="geometry">
          <rect>
           <x>90</x>
           <y>120</y>
           <width>91</width>
           <height>41</height>
          </rect>
         </property>
         <property name="text">
          <string>Level</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QPushButton" name="pushButtonNC3">
         <property name="geometry">
          <rect>
           <x>110</x>
           <y>410</y>
           <width>75</width>
           <height>23</height>
          </rect>
         </property>
         <property name="text">
          <string>NC3</string>
         </property>
         <property name="checkable">
          <bool>false</bool>
         </property>
        </widget>
        <widget class="QLineEdit" name="lineEditNoiseReduction">
         <property name="geometry">
          <rect>
           <x>70</x>
           <y>10</y>
           <width>121</width>
           <height>21</height>
          </rect>
         </property>
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="text">
          <string>Noise Reduction</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="DialS1M5" name="dialNoiseReduction" native="true">
         <property name="geometry">
          <rect>
           <x>100</x>
           <y>60</y>
           <width>90</width>
           <height>41</height>
          </rect>
         </property>
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
        </widget>
        <widget class="QWidget" name="ncWidget" native="true">
         <property name="geometry">
          <rect>
           <x>50</x>
           <y>170</y>
           <width>169</width>
           <height>192</height>
          </rect>
         </property>
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <layout class="QGridLayout" name="gridLayout" rowstretch="8,14,6,14,8" columnstretch="11,38,11">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
          <item row="0" column="1">
           <spacer name="verticalSpacer_2">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="1" column="1">
           <widget class="QPushButton" name="pushButtonNC1">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="checkable">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="2" column="1">
           <spacer name="verticalSpacer">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="2" column="2">
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="3" column="1">
           <widget class="QPushButton" name="pushButtonNC2">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="checkable">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item row="4" column="1">
           <spacer name="verticalSpacer_3">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="ncBypassWidget" native="true">
         <property name="geometry">
          <rect>
           <x>100</x>
           <y>520</y>
           <width>101</width>
           <height>61</height>
          </rect>
         </property>
         <layout class="QGridLayout" name="gridLayout_2" rowstretch="11,14,10" columnstretch="11,38,11">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
          <item row="0" column="1">
           <spacer name="verticalSpacer_4">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>1</width>
              <height>1</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="1" column="0">
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>1</width>
              <height>1</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="1" column="1">
           <widget class="QPushButton" name="pushButtonBypassNoise">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <spacer name="horizontalSpacer_4">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>1</width>
              <height>1</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="2" column="1">
           <spacer name="verticalSpacer_5">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>1</width>
              <height>1</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget3" native="true">
        <widget class="QLabel" name="labelDry">
         <property name="geometry">
          <rect>
           <x>20</x>
           <y>70</y>
           <width>54</width>
           <height>16</height>
          </rect>
         </property>
         <property name="text">
          <string>Dry </string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelRoomSize">
         <property name="geometry">
          <rect>
           <x>100</x>
           <y>130</y>
           <width>62</width>
           <height>16</height>
          </rect>
         </property>
         <property name="text">
          <string>Room Size</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelReverbRoomSmall">
         <property name="geometry">
          <rect>
           <x>40</x>
           <y>200</y>
           <width>41</width>
           <height>51</height>
          </rect>
         </property>
         <property name="text">
          <string>Small</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelReverbRoomLarge">
         <property name="geometry">
          <rect>
           <x>170</x>
           <y>200</y>
           <width>81</width>
           <height>31</height>
          </rect>
         </property>
         <property name="text">
          <string>Large</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelReverbDecayMin">
         <property name="geometry">
          <rect>
           <x>60</x>
           <y>410</y>
           <width>22</width>
           <height>15</height>
          </rect>
         </property>
         <property name="text">
          <string>Max</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelDecayRate">
         <property name="geometry">
          <rect>
           <x>90</x>
           <y>300</y>
           <width>65</width>
           <height>15</height>
          </rect>
         </property>
         <property name="text">
          <string>Decay Rate</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelReverbDecayMax">
         <property name="geometry">
          <rect>
           <x>180</x>
           <y>410</y>
           <width>101</width>
           <height>20</height>
          </rect>
         </property>
         <property name="text">
          <string>Min</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelWet">
         <property name="geometry">
          <rect>
           <x>210</x>
           <y>70</y>
           <width>54</width>
           <height>16</height>
          </rect>
         </property>
         <property name="text">
          <string>Wet</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLineEdit" name="lineEditReverb">
         <property name="geometry">
          <rect>
           <x>90</x>
           <y>10</y>
           <width>91</width>
           <height>21</height>
          </rect>
         </property>
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="text">
          <string>Reverb</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="DialS1M5" name="dialDryWet" native="true">
         <property name="geometry">
          <rect>
           <x>100</x>
           <y>60</y>
           <width>90</width>
           <height>41</height>
          </rect>
         </property>
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
        </widget>
        <widget class="DialS1M1" name="dialRoom" native="true">
         <property name="geometry">
          <rect>
           <x>90</x>
           <y>190</y>
           <width>61</width>
           <height>71</height>
          </rect>
         </property>
        </widget>
        <widget class="DialS1M1" name="dialDecay" native="true">
         <property name="geometry">
          <rect>
           <x>110</x>
           <y>330</y>
           <width>61</width>
           <height>71</height>
          </rect>
         </property>
        </widget>
        <widget class="QWidget" name="rbWidget" native="true">
         <property name="geometry">
          <rect>
           <x>50</x>
           <y>460</y>
           <width>171</width>
           <height>161</height>
          </rect>
         </property>
         <layout class="QGridLayout" name="gridLayout_3" rowstretch="8,14,6,14,6,14,6,14,8" columnstretch="11,38,11">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
          <item row="0" column="1">
           <spacer name="verticalSpacer_6">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="1" column="1">
           <widget class="QPushButton" name="pushButtonReverbStudio">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="checkable">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <spacer name="verticalSpacer_7">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="3" column="1">
           <widget class="QPushButton" name="pushButtonReverbLive">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="checkable">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item row="4" column="0">
           <spacer name="horizontalSpacer_5">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="4" column="1">
           <spacer name="verticalSpacer_8">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="4" column="2">
           <spacer name="horizontalSpacer_6">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="5" column="1">
           <widget class="QPushButton" name="pushButtonReverbHall">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="checkable">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item row="6" column="1">
           <spacer name="verticalSpacer_9">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="7" column="1">
           <widget class="QPushButton" name="pushButtonBypassReverb">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item row="8" column="1">
           <spacer name="verticalSpacer_10">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget4" native="true">
        <widget class="QLabel" name="labelFxIn">
         <property name="geometry">
          <rect>
           <x>10</x>
           <y>55</y>
           <width>53</width>
           <height>20</height>
          </rect>
         </property>
         <property name="text">
          <string>FX</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelFxOut">
         <property name="geometry">
          <rect>
           <x>90</x>
           <y>60</y>
           <width>53</width>
           <height>20</height>
          </rect>
         </property>
         <property name="text">
          <string>FX</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelFxMeterIn">
         <property name="geometry">
          <rect>
           <x>10</x>
           <y>90</y>
           <width>53</width>
           <height>20</height>
          </rect>
         </property>
         <property name="text">
          <string>IN</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
         </property>
        </widget>
        <widget class="QLabel" name="labelFxMeterOut">
         <property name="geometry">
          <rect>
           <x>90</x>
           <y>95</y>
           <width>53</width>
           <height>20</height>
          </rect>
         </property>
         <property name="text">
          <string>OUT</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
         </property>
        </widget>
        <widget class="VolumeMeterS1M7" name="widgetVolumeMeter" native="true">
         <property name="geometry">
          <rect>
           <x>20</x>
           <y>140</y>
           <width>141</width>
           <height>361</height>
          </rect>
         </property>
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
        </widget>
        <widget class="QWidget" name="mutefxWidget" native="true">
         <property name="geometry">
          <rect>
           <x>30</x>
           <y>560</y>
           <width>101</width>
           <height>61</height>
          </rect>
         </property>
         <layout class="QGridLayout" name="gridLayout_4" rowstretch="11,14,10" columnstretch="11,38,11">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
          <item row="0" column="1">
           <spacer name="verticalSpacer_11">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>1</width>
              <height>1</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="1" column="0">
           <spacer name="horizontalSpacer_7">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>1</width>
              <height>1</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="1" column="2">
           <spacer name="horizontalSpacer_8">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>1</width>
              <height>1</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="2" column="1">
           <spacer name="verticalSpacer_12">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>1</width>
              <height>1</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="1" column="1">
           <widget class="QPushButton" name="pushButtonMuteFx">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>VolumeMeterS1M7</class>
   <extends>QWidget</extends>
   <header location="global">volumemeters1m7.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>DialS1M5</class>
   <extends>QWidget</extends>
   <header location="global">dials1m5.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>DialS1M1</class>
   <extends>QWidget</extends>
   <header>dials1m1.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
