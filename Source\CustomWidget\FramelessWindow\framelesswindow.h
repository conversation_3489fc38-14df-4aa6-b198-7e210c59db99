#ifndef FRAMELESSWINDOW_H
#define FRAMELESSWINDOW_H

#include <QDialog>

class QLabel;
class QPushButton;
class QVBoxLayout;
class QGraphicsDropShadowEffect;
class QFrame;
class FramelessWindow : public QDialog
{
    Q_OBJECT
public:
    typedef enum{
        Mode_Normal,
        Mode_Custom
    }Mode;

    typedef enum{
        TitleAlign_Left,
        TitleAlign_Center,
        TitleAlign_Right
    }TitleAlign;

    typedef enum{
        WindowButton_None = 0x0,
        WindowButton_Min = 0x1,
        WindowButton_Close = 0x2,
        WindowButton_All = WindowButton_Min | WindowButton_Close
    }WindowButton;

public:
    explicit FramelessWindow(QWidget *parent = nullptr);
    void setMovable(bool movable);
    void setResizable(bool resizable);
    void setRightBottomDraggable();
    void setTitle(const QString& title);
    void setTitleAlign(TitleAlign align);
    void setWindowButton(WindowButton button);
    void setCentralWidget(QWidget *widget);
    void setHRatio(int titleHRatio, int centralHRatio);
    void setTitleColor(const QColor& color);
    void setTitleBackground(const QColor& background);
    void setFont(const QFont& font);
    void setMode(Mode mode);
    void setCloseButtonReturnCode(int code);
    int getCloseButtonReturnCode();
    void setShadowRadius(int radius);
    void setShadowOffset(const QPoint& offset);
    void setShadowColor(const QColor& color);
    void setShadowVisible(bool isVisible);
    void setMinimumSize(int w, int h);
    void setGeometry(const QRect& rect);
    void setGeometry(int x, int y, int w, int h);
    void resize(int w, int h);
    void setParent(QWidget* parent);
    void setRestore(bool isRestore);
    void restoreWindow();

protected:
    bool event(QEvent* event)override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void resizeEvent(QResizeEvent* event)override;
    void showEvent(QShowEvent* event)override;

private:
    void adjustGeometry();

private:
    enum ResizeRegionFlag {
        None   = 0x0,
        Left   = 0x1,
        Right  = 0x2,
        Top    = 0x4,
        Bottom = 0x8
    };
    Q_DECLARE_FLAGS(ResizeRegion, ResizeRegionFlag)
    void updateResizeRegion(const QPointF &pos);
    void updateCursorShape(const QPointF &pos);

signals:
    void buttonClicked(WindowButton button);
private:
    QColor m_titleColor{161,161,161};
    QColor m_titleBackground{31,31,31};
    QColor m_centralWidgetBackground{31,31,31};
    bool m_isFirst = true;
    QVBoxLayout* m_mainLayout = nullptr;
    QWidget* m_contentWidget = nullptr;
    QVBoxLayout *m_contentWidgetLayout = nullptr;
    QWidget *m_centralWidget = nullptr;
    QVBoxLayout *m_centralWidgetLayout = nullptr;
    int m_shadowRadius = 20;
    int m_edge = 30;
    bool m_movable;
    bool m_resizable;
    bool m_mousePressed;
    bool m_resizing;
    QPointF m_mousePressPos;
    QRect m_windowRect;
    ResizeRegion m_resizeRegion;
    QWidget *m_titleBar;
    QLabel *m_titleLabel;
    QPushButton *m_closeButton;
    QPushButton *m_minButton;
    ResizeRegion m_goalRegion;
    TitleAlign m_titleAlign = TitleAlign_Center;
    WindowButton m_windowButton = WindowButton_Close;
    int m_closeButtonReturnCode = 0;
    QGraphicsDropShadowEffect* m_shadow;
    int m_borderRadius=0;
    QSize m_minimumSize;
    bool m_isRestore = false;
};

#endif // FRAMELESSWINDOW_H
