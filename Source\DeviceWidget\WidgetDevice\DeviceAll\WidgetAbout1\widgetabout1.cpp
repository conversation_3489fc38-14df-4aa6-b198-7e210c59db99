#include "widgetabout1.h"
#include "ui_widgetabout1.h"
#include "globalfont.h"
#include <qdesktopservices.h>
#include <qevent.h>
#include <qlabel.h>
#include <QDesktopServices>
#include "updaterfactory.h"

WidgetAbout1::WidgetAbout1(QWidget *parent, const QString &name)
    : QWidget(parent), AppSettingsObserver(name)
    , ui(new Ui::WidgetAbout1)
{
    ui->setupUi(this);
    ui->button5->setCursor(QCursor(Qt::PointingHandCursor));
    setStyleSheet("color:white");
    AppSettingsSubject::instance().addObserver(this);
    connect(ui->button3, &QPushButton::clicked, this, [this]() {
        if(m_firmwareUpdateStatus == UpdateStatus_Available) {
            emit attributeChanged(objectName(), "UpdateFirmware", "1");
        } else if(m_firmwareUpdateStatus == UpdateStatus_Error) {
            setFirmwareText("V" + UPDATER_INSTANCE(Firmware)->getCurVersion()+ (m_language == "English" ? "  =>  Fetching..." : QStringLiteral("  =>  获取中...")));
            UPDATER_INSTANCE(Firmware)->fetchUpdateData();
            showEvent(nullptr);
        }
    });
    connect(ui->button4, &QPushButton::clicked, this, [this]() {
        if(m_softwareUpdateStatus == UpdateStatus_Available) {
            emit attributeChanged(objectName(), "UpdateSoftware", "1");
        } else if(m_softwareUpdateStatus == UpdateStatus_Error) {
            QString beta;
            if(UPDATER_INSTANCE(Software)->getCurVersion().section('.', -1) != QChar('0')){
                beta = " Beta";
            }
            setSoftwareText("V" + UPDATER_INSTANCE(Software)->getCurVersion() + beta + (m_language == "English" ? "  =>  Fetching..." : QStringLiteral("  =>  获取中...")));
            UPDATER_INSTANCE(Software)->fetchUpdateData();
            showEvent(nullptr);
        }
    });

    connect(ui->button5, &QPushButton::clicked, this, [this]() {
        QDesktopServices::openUrl(QUrl("https://www.topping.pro/"));
    });
}

WidgetAbout1::~WidgetAbout1()
{
    delete ui;
}

void WidgetAbout1::setName(const QString &name)
{
    setObjectName(name);
    AppSettingsObserver::setObserverName(name);
}

void WidgetAbout1::setFont(const QFont &font)
{
    m_font = font;
}
void WidgetAbout1::changeLanguage(QString language)
{
    m_language = language;
    if (m_language == "Chinese") {
        ui->label1->setText("设备型号");
        ui->label2->setText("设备硬件版本");
        ui->label3->setText("设备固件版本");
        ui->label4->setText("设备软件版本");
        auto setStatusText = [](UpdaterFactory::UpdaterType updaterType, UpdateStatus status, QLabel *label, QPushButton *button) {
            if(status == UpdateStatus_Available) {
                button->setText("更新");
            } else if(status == UpdateStatus_Error) {
                QString beta;
                if(updaterType == UpdaterFactory::UpdaterType::Software && UpdaterFactory::getInstance(updaterType)->getCurVersion().section('.', -1) != QChar('0')){
                    beta = " Beta";
                }
                label->setText("V" + UpdaterFactory::getInstance(updaterType)->getCurVersion() + beta+QStringLiteral("  =>  获取失败"));
                button->setText("重试");
            }
        };
        setStatusText(UpdaterFactory::UpdaterType::Firmware, m_firmwareUpdateStatus, ui->labelFirmwareTip, ui->button3);
        setStatusText(UpdaterFactory::UpdaterType::Software, m_softwareUpdateStatus, ui->labelSoftwareTip, ui->button4);
        ui->label5->setText("访问官网");
    } else if(m_language == "English") {
        ui->label1->setText("Model");
        ui->label2->setText("Hardware version");
        ui->label3->setText("Firmware version");
        ui->label4->setText("Software version");
        auto setStatusText = [](UpdaterFactory::UpdaterType updaterType, UpdateStatus status, QLabel* label, QPushButton* button) {
            auto curVersion = UpdaterFactory::getInstance(updaterType)->getCurVersion();
            if (status == UpdateStatus_Available) {
                button->setText("Update");
            }
            else if (status == UpdateStatus_Error) {
                QString beta;
                if(updaterType == UpdaterFactory::UpdaterType::Software && UpdaterFactory::getInstance(updaterType)->getCurVersion().section('.', -1) != QChar('0')){
                    beta = " Beta";
                }
                label->setText("V" + UpdaterFactory::getInstance(updaterType)->getCurVersion() + beta+ QStringLiteral("  =>  Fetch failed"));
                button->setText("Retry");
            }
        };
        setStatusText(UpdaterFactory::UpdaterType::Firmware, m_firmwareUpdateStatus, ui->labelFirmwareTip, ui->button3);
        setStatusText(UpdaterFactory::UpdaterType::Software, m_softwareUpdateStatus, ui->labelSoftwareTip, ui->button4);
        ui->label5->setText("Official website");
    }
}

void WidgetAbout1::setFirmwareUpdateStatus(UpdateStatus status)
{
    m_firmwareUpdateStatus = status;
    switch (status) {
    case UpdateStatus_None:
        ui->button3->hide();
        break;
    case UpdateStatus_Available:
        ui->button3->show();
        break;
    case UpdateStatus_Error:
        ui->button3->show();
        break;        
    }
    resizeEvent(nullptr);
    changeLanguage(m_language);
}

void WidgetAbout1::setSoftwareUpdateStatus(UpdateStatus status)
{
    m_softwareUpdateStatus = status;
    switch (status) {
    case UpdateStatus_None:
        ui->button4->hide();
        break;
    case UpdateStatus_Available:
        ui->button4->show();
        break;
    case UpdateStatus_Error:
        ui->button4->show();
        break;        
    }
    resizeEvent(nullptr);
    changeLanguage(m_language);
}

void WidgetAbout1::setDeviceName(const QString &name)
{
    ui->labelModel->setText(name);
}

void WidgetAbout1::setHardwareVersion(const QString &version)
{
    ui->labelHard->setText("V"+version);
}

void WidgetAbout1::setFirmwareText(const QString &text)
{
    ui->labelFirmwareTip->setText(text);
}

void WidgetAbout1::setSoftwareText(const QString & text)
{
    ui->labelSoftwareTip->setText(text);
}

int WidgetAbout1::itemCount()const
{
    int widgetCount = 0;
    for(int i = 0; i < ui->verticalLayout->count(); i++) {
        if( ui->verticalLayout->itemAt(i)->widget() != nullptr) {
            widgetCount++;
        }
    }
    return widgetCount;
}

void WidgetAbout1::resizeEvent(QResizeEvent *event)
{
    int vSpace = 0.12 * ui->widget1->height();
    int iconWH = ui->widget1->height()-2*vSpace;
    int iconY = vSpace;
    int keyWidth = ui->widget1->width() * 0.5;
    int valueWidth = ui->widget1->width() - keyWidth;
    {
        double wRatio = ui->widget1->width()/100.0;
        ui->label1->setGeometry(0, iconY, keyWidth, iconWH);
        ui->labelModel->setGeometry(keyWidth, iconY, valueWidth, iconWH);
    }
    {
        double wRatio = ui->widget2->width()/100.0;
        ui->label2->setGeometry(0, iconY, keyWidth, iconWH);
        ui->labelHard->setGeometry(keyWidth, iconY, valueWidth, iconWH);
    }
    {
        double wRatio = ui->widget3->width()/100.0;
        int buttonW = ui->widget1->width() *0.1;
        int labelTipW = ui->widget1->width() *0.5;
        int hSpace = ui->widget1->width() *0.02;
        int keyWidth = ui->widget1->width() - buttonW- labelTipW - hSpace;
        ui->label3->setGeometry(0, iconY, keyWidth, iconWH);
        if(m_firmwareUpdateStatus == UpdateStatus_Available || m_firmwareUpdateStatus == UpdateStatus_Error) {
            ui->button3->setGeometry(keyWidth+labelTipW+hSpace, iconY, buttonW, iconWH);
            ui->labelFirmwareTip->setGeometry(keyWidth, iconY, labelTipW, iconWH);
        }else if(m_firmwareUpdateStatus == UpdateStatus_None) {
            ui->labelFirmwareTip->setGeometry(keyWidth, iconY, buttonW+labelTipW+hSpace, iconWH);
        }
    }
    {
        double wRatio = ui->widget4->width()/100.0;
        int buttonW = ui->widget1->width() *0.1;
        int labelTipW = ui->widget1->width() *0.5;
        int hSpace = ui->widget1->width() *0.02;
        int keyWidth = ui->widget1->width() - buttonW- labelTipW - hSpace;
        ui->label4->setGeometry(0, iconY, keyWidth, iconWH);
        if(m_softwareUpdateStatus == UpdateStatus_Available || m_softwareUpdateStatus == UpdateStatus_Error) {
            ui->button4->setGeometry(keyWidth+labelTipW+hSpace, iconY, buttonW, iconWH);
            ui->labelSoftwareTip->setGeometry(keyWidth, iconY, labelTipW, iconWH);
        }else if(m_softwareUpdateStatus == UpdateStatus_None) {
            ui->labelSoftwareTip->setGeometry(keyWidth, iconY, buttonW+labelTipW+hSpace, iconWH);
        }
    }
    {
        double wRatio = ui->widget5->width()/100.0;
        int valueWidth = ui->widget1->width() *0.17;
        int keyWidth = ui->widget1->width() - valueWidth;
        ui->label5->setGeometry(0, iconY, keyWidth, iconWH);
        ui->button5->setGeometry(keyWidth, iconY, valueWidth, iconWH);
    }

    m_font.setPointSizeF(GLBFHandle.getSuitablePointSize(m_font, this->height()*0.053));
    ui->label1->setFont(m_font);
    ui->label2->setFont(m_font);
    ui->label3->setFont(m_font);
    ui->label4->setFont(m_font);
    ui->label5->setFont(m_font);
    ui->labelModel->setFont(m_font);
    ui->labelHard->setFont(m_font);
    ui->labelFirmwareTip->setFont(m_font);
    ui->button3->setFont(m_font);
    ui->labelSoftwareTip->setFont(m_font);
    ui->button4->setFont(m_font);
    ui->button5->setFont(m_font);

    ui->button3->setStyleSheet(QString("background:rgba(67, 207, 124, 1);border-radius:%1px").arg(ui->button3->height()*0.2));
    ui->button4->setStyleSheet(QString("background:rgba(67, 207, 124, 1);border-radius:%1px").arg(ui->button4->height()*0.2));
}

void WidgetAbout1::showEvent(QShowEvent *event)
{
    setFirmwareInfo();
    setSoftwareInfo();
}

void WidgetAbout1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    if (attribute == "Language") {
        changeLanguage(value);
    }
}

void WidgetAbout1::setFirmwareInfo()
{
    QString newFirmwareVersion;
    auto object = UPDATER_INSTANCE(Firmware)->getUpdateData();
    newFirmwareVersion = object.value("Device").toObject().value(ui->labelModel->text()).toObject().value("Version").toString();
    UpdateStatus status = UpdateStatus_Error;
    QString curFirmwareVersion = UPDATER_INSTANCE(Firmware)->getCurVersion();
    QString firmwareText("V" + curFirmwareVersion+ (m_language=="English"?"  =>  Fetch failed":QStringLiteral("  =>  获取失败")));
    if(!newFirmwareVersion.isEmpty()) {
        bool isNeedUpdate = curFirmwareVersion < newFirmwareVersion;
        status = UpdateStatus_None;
        firmwareText = "V" + curFirmwareVersion;
        if(isNeedUpdate) {
            status = UpdateStatus_Available;
            auto curV = curFirmwareVersion;
            firmwareText = QStringLiteral("V%1  =>  V%2").arg(curV).arg(newFirmwareVersion);
        }
    }
    setFirmwareText(firmwareText);
    setFirmwareUpdateStatus(status);
}

void WidgetAbout1::setSoftwareInfo()
{
    QString newSoftwareVersion;
    auto object = UPDATER_INSTANCE(Software)->getUpdateData();
#ifdef Q_OS_WIN
        newSoftwareVersion = object.value("Software").toObject().value("Win").toObject().value("Version").toString();
#endif
#ifdef Q_OS_MACOS
        newSoftwareVersion = object.value("Software").toObject().value("Mac").toObject().value("Version").toString();
#endif
    UpdateStatus status = UpdateStatus_Error;
    QString curSoftwareVersion = UPDATER_INSTANCE(Software)->getCurVersion();
    QString beta;
    if(curSoftwareVersion.section('.', -1)!= QChar('0')){
        beta = " Beta";
    }
    QString softwareText("V" + curSoftwareVersion +beta+ (m_language == "English" ? "  =>  Fetch failed" : QStringLiteral("  =>  获取失败")));
    if (!newSoftwareVersion.isEmpty()) {
        bool isNeedUpdate = curSoftwareVersion < newSoftwareVersion;
        status = UpdateStatus_None;
        softwareText = "V" + curSoftwareVersion+beta;
        if (isNeedUpdate) {
            status = UpdateStatus_Available;
            auto curV = curSoftwareVersion+beta;
            beta="";
            if(newSoftwareVersion.section('.', -1) != QChar('0')){
                beta = " Beta";
            }
            softwareText = QStringLiteral("V%1  =>  V%2").arg(curV).arg(newSoftwareVersion+beta);
        }
    }
    setSoftwareText(softwareText);
    setSoftwareUpdateStatus(status);
}
