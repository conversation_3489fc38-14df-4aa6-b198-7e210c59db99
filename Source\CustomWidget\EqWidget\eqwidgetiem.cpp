#include "eqwidgetiem.h"
#include <QApplication>
#include <QHash>
#include <functional>
#include <QVariant>
#include <QLabel>
#include <QCheckBox>
#include <QFont>
#include <QResizeEvent>
#include "comboboxs1m3.h"
#include "dials1m5.h"
#include "globalfont.h"

EqWidgetIem::EqWidgetIem(int index, QWidget* parent)
    : QFrame(parent)
    , mIndex(index)
    , mSizeFactor(1.0)
    , mMinimumWidth(50)
    , mItemLabel(nullptr)
    , mTypeComboBox(nullptr)
    , mGainDial(nullptr)
    , mFrequencyDial(nullptr)
    , mQValueDial(nullptr)
    , mEnabledCheckBox(nullptr)
{
    mData.type = "high pass";
    mData.gain = 0.0f;
    mData.frequency = 1000.0f;
    mData.qValue = 0.7f;
    mData.enabled = true;

    setupUI();
    setupConnections();
    setStyleSheet(
        "EqWidgetIem {"
        "    background-color: rgb(22,22,22);"
        "}"
        "QLabel {"
        "    color: #404040;"
        "}"
        "QCheckBox::indicator {"
        "    width: 20px;"
        "    height: 20px;"
        "    border-radius: 3px;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #43cf7c;"
        "}"
        "QCheckBox::indicator:checked {"
        "    image:url(:/Icon/checkboxTick.svg);"
        "}"
        "QCheckBox::indicator:unchecked {"
        "    background-color: #43cf7c;"
        "}"
    );
}

EqWidgetIem::~EqWidgetIem()
{
}

void EqWidgetIem::setupUI()
{
    setMinimumWidth(100);

    // 创建控件，使用手动布局
    mItemLabel = new QLabel(QString::number(mIndex + 1), this);
    mItemLabel->setAlignment(Qt::AlignCenter);

    mTypeComboBox = new ComboBoxS1M3(this);
    mTypeComboBox->setIndicatorWHRatio(0.5);
    QVector<QString> types = {
        "high pass",
        "Low pass",
        "High Shelf",
        "High Shelf"
    };
    mTypeComboBox->addItems(types);

    mGainDial = new DialS1M5(this);
    mGainDial->setRange(-20.0f, 20.0f)
              .setValue(0.0f)
              .setStep(0.1f)
              .setPrecision(1)
              .showSign(true);

    mFrequencyDial = new DialS1M5(this);
    mFrequencyDial->setRange(20.0f, 20000.0f)
                  .setValue(1000.0f)
                  .setStep(1.0f)
                  .setPrecision(0);

    mQValueDial = new DialS1M5(this);
    mQValueDial->setRange(0.1f, 10.0f)
                .setValue(0.7f)
                .setStep(0.1f)
                .setPrecision(1);

    mEnabledCheckBox = new QCheckBox(this);
    mEnabledCheckBox->setChecked(true);
}

void EqWidgetIem::setupConnections()
{
    static QHash<EqWidgetItemData::ChangeFlag, std::function<void(const QVariant&)>> changeHandlers = {
        { EqWidgetItemData::TYPE_CHANGED, [this](const QVariant& arg) {
             mData.updateType(mTypeComboBox->currentText());
        }},
        { EqWidgetItemData::GAIN_CHANGED, [this](const QVariant& arg) {
            mData.updateGain(arg.toFloat());
        }},
        { EqWidgetItemData::FREQUENCY_CHANGED, [this](const QVariant& arg) {
            mData.updateFrequency(arg.toFloat());
        }},
        { EqWidgetItemData::QVALUE_CHANGED, [this](const QVariant& arg) {
            mData.updateQValue(arg.toFloat());
        }},
        { EqWidgetItemData::ENABLED_CHANGED, [this](const QVariant& arg) {
            mData.updateEnabled(arg.toBool());
        }}
    };
    auto* changeHandlersPtr = &changeHandlers;
    static auto handleAndEmit =[this, changeHandlersPtr](EqWidgetItemData::ChangeFlag flag, const QVariant& arg){
        changeHandlersPtr->value(flag)(arg);
        emit dataChanged(mIndex, mData);
        mData.clearAllChanged();
    };
    auto* handleAndEmitPtr = &handleAndEmit;

    connect(mTypeComboBox, &ComboBoxS1M3::currentIndexChanged, this, [handleAndEmitPtr](int index) {
        (*handleAndEmitPtr)(EqWidgetItemData::TYPE_CHANGED, QVariant(index));
    });
    connect(mGainDial, &DialS1M5::valueChanged, this, [handleAndEmitPtr](float value) {
        (*handleAndEmitPtr)(EqWidgetItemData::GAIN_CHANGED, QVariant(value));
    });
    connect(mFrequencyDial, &DialS1M5::valueChanged, this, [handleAndEmitPtr](float value) {
        (*handleAndEmitPtr)(EqWidgetItemData::FREQUENCY_CHANGED, QVariant(value));
    });
    connect(mQValueDial, &DialS1M5::valueChanged, this, [handleAndEmitPtr](float value) {
        (*handleAndEmitPtr)(EqWidgetItemData::QVALUE_CHANGED, QVariant(value));
    });
    connect(mEnabledCheckBox, &QCheckBox::toggled, this, [handleAndEmitPtr](bool enabled) {
        (*handleAndEmitPtr)(EqWidgetItemData::ENABLED_CHANGED, QVariant(enabled));
    });
}

EqWidgetIem& EqWidgetIem::setFont(QFont font)
{
    mFont = font;
    mTypeComboBox->setFont(mFont);
    mGainDial->setFont(mFont);
    mFrequencyDial->setFont(mFont);
    mQValueDial->setFont(mFont);
    resizeEvent(nullptr);
    return *this;
}

void EqWidgetIem::setSizeFactor(double sizeFactor)
{
    mSizeFactor = sizeFactor;
    setFixedWidth(mMinimumWidth * sizeFactor);
    adjustFontAndSize();
}

void EqWidgetIem::setItemData(const EqWidgetItemData& data)
{
    mData = data;
    mTypeComboBox->setCurrentText(data.type);
    mGainDial->setValue(data.gain);
    mFrequencyDial->setValue(data.frequency);
    mQValueDial->setValue(data.qValue);
    mEnabledCheckBox->setChecked(data.enabled);
}

EqWidgetItemData EqWidgetIem::getItemData() const
{
    return mData;
}

void EqWidgetIem::setItemIndex(int index)
{
    mIndex = index;
    mItemLabel->setText(QString::number(index + 1));
}

void EqWidgetIem::setMinimumItemWidth(int width)
{
    mMinimumWidth = width;
    setMinimumWidth(width);
}

void EqWidgetIem::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);

    // 手动布局控件
    if (!mItemLabel || !mTypeComboBox || !mGainDial || !mFrequencyDial || !mQValueDial || !mEnabledCheckBox)
        return;

    int w = width();
    int h = height();

    // 计算每个控件的高度和位置
    double perHeight = h / 15.0;
    int currentY = 0;

    // 标签
    int labelHeight = perHeight * 1.5;
    mItemLabel->setGeometry(0, currentY, w, labelHeight);
    currentY += labelHeight;

    // 类型下拉框
    int comboHeight = perHeight * 1.5;
    mTypeComboBox->setGeometry(0, currentY, w, comboHeight);
    currentY += comboHeight + perHeight * 0.5; // 添加间距

    // 增益旋钮
    int dialHeight = perHeight * 3;
    mGainDial->setGeometry(0, currentY, w, dialHeight);
    currentY += dialHeight + perHeight * 0.5; // 添加间距

    // 频率旋钮
    mFrequencyDial->setGeometry(0, currentY, w, dialHeight);
    currentY += dialHeight + perHeight * 0.5; // 添加间距

    // Q值旋钮
    mQValueDial->setGeometry(0, currentY, w, dialHeight);
    currentY += dialHeight + perHeight * 0.5; // 添加间距

    // 启用复选框
    int checkHeight = perHeight;
    mEnabledCheckBox->setGeometry(0, currentY, w, checkHeight);

    adjustFontAndSize();
}

void EqWidgetIem::adjustFontAndSize()
{
    if (!mEnabledCheckBox || !mItemLabel) return;

    // 设置复选框样式
    int checkBoxSize = mEnabledCheckBox->height() * 0.8;
    mEnabledCheckBox->setStyleSheet(QString(
    "QCheckBox::indicator {"
        "width: %1px;"
        "height: %1px;"
        "border-radius: %2px;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #43cf7c;"
        "}"
        "QCheckBox::indicator:checked {"
        "    image:url(:/Icon/checkboxTick.svg);"
        "}"
        "QCheckBox::indicator:unchecked {"
        "    background-color: #43cf7c;"
        "}").arg(checkBoxSize).arg(checkBoxSize * 0.2)
    );

    // 设置字体大小
    QFont labelFont = mFont;
    labelFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height()*0.08));
    mItemLabel->setFont(labelFont);

    QFont controlFont = mFont;
    controlFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height()*0.04));
    mTypeComboBox->setFont(controlFont);
    mGainDial->setFont(controlFont);
    mFrequencyDial->setFont(controlFont);
    mQValueDial->setFont(controlFont);
}
