#ifndef INPUTS1M5_H
#define INPUTS1M5_H


#include <QFont>
#include <QEvent>
#include <QTimer>
#include <QObject>
#include <QWidget>
#include <QResizeEvent>

#include "inputbase.h"
#include "workspace.h"
#include "appsettings.h"
#include "autogains1m1.h"


namespace Ui {
class InputS1M5;
}


class InputS1M5 : public InputBase, public WorkspaceObserver, public AppSettingsObserver
{
    Q_OBJECT
public:
    explicit InputS1M5(QWidget* parent=nullptr, QString name="");
    ~InputS1M5();
    InputS1M5& setName(QString name);
    InputS1M5& setFont(QFont font);
    InputS1M5& setVolumeMeter(int value);
    InputS1M5& setVolumeMeterClear();
    InputS1M5& setVolumeMeterSlip();
    InputS1M5& setGain(float value);
    InputS1M5& setGainLock(bool state=true);
    InputS1M5& setMuteAffectGain(bool state=true);
    InputS1M5& setGainAffectMute(bool state=true);
    InputS1M5& setGainRange(float min, float max);
    InputS1M5& setGainDefault(float value);
    InputS1M5& setGainWidgetDisable(float value);
    InputS1M5& setChannelNameEditable(bool state=true);
    InputS1M5& setValue48V(bool state=true);
    InputS1M5& setValueGAIN(float value);
    InputS1M5& setValueMUTE(bool state=true);
    InputS1M5& setOverlay(bool state=true);
protected:
    bool eventFilter(QObject* obj, QEvent* e) override;
    void resizeEvent(QResizeEvent* e) override;
    void updateAttribute() override;
    void setSoloState(bool state) override;
    void setSoloStateLeft(bool state) override;
    void setSoloStateRight(bool state) override;
    void setMuteState(bool state) override;
    void setMuteStateLeft(bool state) override;
    void setMuteStateRight(bool state) override;
    void setSoloClicked(bool state) override;
    void setSoloClickedLeft(bool state) override;
    void setSoloClickedRight(bool state) override;
    void setMuteClicked(bool state) override;
    void setMuteClickedLeft(bool state) override;
    void setMuteClickedRight(bool state) override;
    bool getSoloState() override;
    bool getSoloStateLeft() override;
    bool getSoloStateRight() override;
    bool getMuteState() override;
    bool getMuteStateLeft() override;
    bool getMuteStateRight() override;
    void loadSettings() override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
private:
    Ui::InputS1M5* ui;
    AutoGainS1M1 mAutoGain;
    QTimer mTimer;
    QFont mFont;
    int mPre48V=-2147483648;
    int mPreMUTE=-2147483648;
    int mPreANTI=-2147483648;
    float mPreGAIN=-2147483648;
    float mDisableGAIN=-88;
    bool mMuteAffectGain=false;
    bool mGainAffectMute=false;
    void save(QAnyStringView key, const QVariant& value);
private slots:
    void in_mAutoGain_attributeChanged(QString attribute, QString value);
    void in_mTimer_timeout();
    void in_widgetPushButtonGroup1_stateChanged(QString button, QString state);
    void in_widgetPushButtonGroup2_stateChanged(QString button, QString state);
    void in_widgetDial_valueChanged(float value);
    void on_lineEdit_textChanged(const QString& arg1);
    void on_lineEdit_editingFinished();
    void on_pushButtonClose_clicked();
};


#endif // INPUTS1M5_H

