#ifndef EQWIDGET_H
#define EQWIDGET_H

#include <QWidget>
#include "eqwidgetitemdata.h"

class QScrollArea;
class QLabel;
class QPushButton;
class EqWidgetIem;
class EqWidget : public QWidget
{
    Q_OBJECT

public:
    explicit EqWidget(QWidget* parent = nullptr);
    ~EqWidget();
    EqWidget& setName(QString name);
    EqWidget& setFont(QFont font);
    void setSizeFactor(double sizeFactor);
    void setItemStretch(double sizeFactor);

    void setItemCount(int count);
    int getItemCount() const { return mItems.size(); }

    void addItem();
    void removeItem(int index = -1);
    void removeAllItems();

    void setEqData(const QVector<EqWidgetItemData>& data);
    QVector<EqWidgetItemData> getEqData() const;
    
protected:
    void resizeEvent(QResizeEvent* event) override;
    void adjustFontAndSize();
    void ajudstMargin();
    void updateItemSize();

private:
    void setupUI();
    void updateItemIndices();
    void createDefaultItems();

    QFont mFont;
    double mSizeFactor;
    bool mUpdateSize;

    QScrollArea* mScrollArea;
    QWidget* mScrollWidget;
    QLabel* mTitleTypeLabel;
    QLabel* mTitleGainLabel;
    QLabel* mTitleFrequencyLabel;
    QLabel* mTitleQLabel;

    QPushButton* mAddItemButton;
    QPushButton* mRemoveItemButton;

    QVector<EqWidgetIem*> mItems;

signals:
    void itemDataChanged(int index, const EqWidgetItemData& data);
    void ItemCountChanged(int count);
    void attributeChanged(QString objectName, QString attribute, QString value);
};

#endif // EQWIDGET_H
