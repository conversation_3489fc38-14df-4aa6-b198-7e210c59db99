#ifndef FIELDLOOPBACKBASE1_H
#define FIELDLOOPBACKBASE1_H


#include <QPair>
#include <QFont>
#include <QRect>
#include <QLabel>
#include <QColor>
#include <QWidget>
#include <QVector>
#include <QString>
#include <QPainter>
#include <QScrollArea>
#include <QPaintEvent>
#include <QPushButton>
#include <QResizeEvent>

#include "loopbackbase.h"
#include "buttonboxs1m1.h"


class FieldLoopbackBase1 : public QWidget
{
    Q_OBJECT
public:
    explicit FieldLoopbackBase1(QWidget* parent=nullptr);
    ~FieldLoopbackBase1();
    FieldLoopbackBase1& modifyWidgetList(QVector<LoopbackBase*> list);
    FieldLoopbackBase1& setFont(QFont font);
    FieldLoopbackBase1& setVisibleList(QVector<QString> list);
    FieldLoopbackBase1& setFieldTitle(QString text);
    FieldLoopbackBase1& setFieldColor(QColor color);
    FieldLoopbackBase1& setWidgetAreaColor(QColor color);
    FieldLoopbackBase1& setWidgetAreaVisible(bool state=true);
    FieldLoopbackBase1& setFieldHeadAreaStretchFactor(float factor);
    FieldLoopbackBase1& setAdditionVisible(bool state=true);
    FieldLoopbackBase1& setAdditionButtonWeight(int weightWidth, int weightHeight);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
private:
    struct MixerWidget
    {
        bool visible;
        LoopbackBase* widget;
    };
    QFont mFont;
    float mHeadStretchFactor=5.3;
    QColor mColorBG=QColor(31, 31, 31);
    QColor mColorWidgetArea=QColor(128, 128, 128);
    QRect mRectHead;
    QRect mRectBody;
    QRect mRectWidgetArea;
    QLabel mLabelTitle;
    QScrollArea mScrollArea;
    int mScrollBarValue=0;
    QWidget mWidget;
    QVector<MixerWidget*> mWidgetList;
    ButtonBoxS1M1 mWidgetAddition;
    bool mWidgetAreaVisible=true;
    bool mAdditionVisible=true;
    void drawBG(QPainter* painter);
    void drawWidgetArea(QPainter* painter);
private slots:
    void in_mScrollArea_valueChanged(int value);
    void in_mWidgetListAll_attributeChanged(QString objectName, QString attribute, QString value);
    void in_mWidgetAddition_attributeChanged(QString objectName, QString attribute, QString value);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // FIELDLOOPBACKBASE1_H

