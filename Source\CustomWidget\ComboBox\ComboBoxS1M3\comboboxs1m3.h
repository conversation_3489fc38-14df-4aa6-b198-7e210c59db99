#ifndef COMBOBOXS1M3_H
#define COMBOBOXS1M3_H


#include <QComboBox>

class ComboBoxS1M3 : public QComboBox
{
    Q_OBJECT
public:
    explicit ComboBoxS1M3(QWidget* parent=nullptr);
    void setFont(const QFont& font);
    void setIndicatorWHRatio(double ratio);

protected:
    bool eventFilter(QObject *watched, QEvent *event)override;
    void mousePressEvent(QMouseEvent* event)override;
    void resizeEvent(QResizeEvent *event) override;
    void wheelEvent(QWheelEvent* event) override;
    void showPopup() override;

private:
    QWidget* m_container=nullptr;
    double m_indicatorWH = 1.0;
};


#endif // COMBOBOXS1M3_H

