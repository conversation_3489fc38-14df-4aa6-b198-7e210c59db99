#include "globalfont.h"
#include "volumemeters1m5.h"


VolumeMeterS1M5::VolumeMeterS1M5(QWidget* parent)
    : QWidget(parent)
{
    mRectMeterOrigin.timerClip.setSingleShot(true);
    mRectMeterGained.timerClip.setSingleShot(true);
    connect(&mRectMeterOrigin.timerClip, SIGNAL(timeout()), this, SLOT(in_timerClipOrigin_timeout()), Qt::UniqueConnection);
    connect(&mRectMeterGained.timerClip, SIGNAL(timeout()), this, SLOT(in_timerClipGained_timeout()), Qt::UniqueConnection);
    connect(&mTimerMeter, SIGNAL(timeout()), this, SLOT(in_mTimerMeter_timeout()), Qt::UniqueConnection);
}
VolumeMeterS1M5::~VolumeMeterS1M5()
{

}


// override
void VolumeMeterS1M5::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wSpace1=wPixelPerRatio * 0;
    int wMeterGained=wPixelPerRatio * mMeterGained;
    int wSpace2=wPixelPerRatio * mSpace2;
    int wMeterOrigin=wPixelPerRatio * mMeterOrigin;
    int wSpace3=wPixelPerRatio * mSpace1;
    int wScale=wPixelPerRatio * mScale;
    int wSpace4=wPixelPerRatio * 0;
    int wRemain=size().width() - wSpace1 - wMeterGained - wSpace2 - wMeterOrigin - wSpace3 - wScale - wSpace4;
    int xMeterGained=0 + wRemain / 2 + wSpace1;
    int xMeterOrigin=xMeterGained + wMeterGained + wSpace2;
    int xScale=xMeterOrigin + wMeterOrigin + wSpace3;
    // H
    float hPixelPerRatio=size().height() / 100.0;
    int hClip=hPixelPerRatio * mHClip;
    int hSpace2=hPixelPerRatio * mHSpace1;
    int hVolume=hPixelPerRatio * mHVolume;
    int hSpace3=hPixelPerRatio * mHSpace2;
    int hRemain=size().height() - hClip - hSpace2 - hVolume - hSpace3;
    int yClip=0 + hRemain / 2;
    int yClipEnd=yClip + hClip;
    int yVolume=yClipEnd + hSpace2;
    mRectMeterGained.clip.setRect(xMeterGained, yClip, wMeterGained, hClip);
    mRectMeterGained.volume.setRect(xMeterGained, yVolume, wMeterGained, hVolume);
    mRectMeterOrigin.clip.setRect(xMeterOrigin, yClip, wMeterOrigin, hClip);
    mRectMeterOrigin.volume.setRect(xMeterOrigin, yVolume, wMeterOrigin, hVolume);
    mRectScale.setRect(xScale, 0, wScale, size().height());
}
void VolumeMeterS1M5::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
    drawElement(&painter);
}
void VolumeMeterS1M5::mouseDoubleClickEvent(QMouseEvent* e)
{
    if(e->button() == Qt::LeftButton)
    {
        if(mRectMeterOrigin.clip.contains(e->pos()))
        {
            mRectMeterOrigin.clipStatus = false;
            update();
        }
        else if(mRectMeterGained.clip.contains(e->pos()))
        {
            mRectMeterGained.clipStatus = false;
            update();
        }
    }
}
void VolumeMeterS1M5::drawBG(QPainter* painter)
{
    painter->save();
    painter->setPen(Qt::NoPen);
    painter->setBrush(mColorBG);
    painter->drawRect(rect());
    painter->restore();
}
void VolumeMeterS1M5::drawElement(QPainter* painter)
{
    float pixelPerScale;
    QPointF textPoint;
    QRect rectMeter;
    painter->save();
    // font
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, "-90", mRectScale.width()));
    painter->setFont(mFont);
    // clip
    painter->setPen(Qt::NoPen);
    mRectMeterOrigin.clipStatus ? (painter->setBrush(QBrush(QColor(246, 72, 71)))) : (painter->setBrush(QBrush(QColor(60, 60, 60))));
    painter->drawRoundedRect(mRectMeterOrigin.clip, mRectMeterOrigin.clip.height() / 3, mRectMeterOrigin.clip.height() / 3);
    mRectMeterGained.clipStatus ? (painter->setBrush(QBrush(QColor(246, 72, 71)))) : (painter->setBrush(QBrush(QColor(60, 60, 60))));
    painter->drawRoundedRect(mRectMeterGained.clip, mRectMeterGained.clip.height() / 3, mRectMeterGained.clip.height() / 3);
    // volume
    painter->setBrush(QBrush(QColor(60, 60, 60)));
    painter->drawRoundedRect(mRectMeterOrigin.volume, mRectMeterOrigin.volume.width() / 3, mRectMeterOrigin.volume.width() / 3);
    painter->drawRoundedRect(mRectMeterGained.volume, mRectMeterGained.volume.width() / 3, mRectMeterGained.volume.width() / 3);

    painter->setPen(Qt::NoPen);
    QLinearGradient gradient(mRectMeterOrigin.volume.topLeft(), mRectMeterOrigin.volume.bottomLeft());
    gradient.setColorAt(0.0, QColor("#f64847"));
    gradient.setColorAt(0.0666, QColor("#a77d43"));
    gradient.setColorAt(0.1333, QColor("#0f9640"));
    gradient.setColorAt(1.0, QColor("#009641"));
    painter->setBrush(gradient);
    pixelPerScale = mRectMeterOrigin.volume.height() / 90.0;
    rectMeter = mRectMeterOrigin.volume;
    rectMeter.setY(rectMeter.y() + pixelPerScale * (0 - mRectMeterOrigin.volumeValue));
    rectMeter.setHeight(rectMeter.bottomLeft().y() - rectMeter.y());
    if(mRectMeterOrigin.volumeValue != -90)
    {
        painter->drawRoundedRect(rectMeter, rectMeter.width() / 3, rectMeter.width() / 3);
    }
    gradient.setStart(mRectMeterGained.volume.topLeft());
    gradient.setFinalStop(mRectMeterGained.volume.bottomLeft());
    painter->setBrush(gradient);
    pixelPerScale = mRectMeterGained.volume.height() / 90.0;
    rectMeter = mRectMeterGained.volume;
    rectMeter.setY(rectMeter.y() + pixelPerScale * (0 - mRectMeterGained.volumeValue));
    rectMeter.setHeight(rectMeter.bottomLeft().y() - rectMeter.y());
    if(mRectMeterGained.volumeValue != -90)
    {
        painter->drawRoundedRect(rectMeter, rectMeter.width() / 3, rectMeter.width() / 3);
    }
    // scale
    int wScaleLine=mRectMeterOrigin.volume.width() / 2;
    painter->setPen(QColor(161, 161, 161));
    painter->setBrush(Qt::NoBrush);
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("0")) / 2);
    textPoint.setY(mRectMeterGained.volume.y() + 0 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width(), textPoint.y() + 1, mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width() + wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "0");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-6")) / 2);
    textPoint.setY(mRectMeterGained.volume.y() + 6 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width(), textPoint.y() + 1, mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width() + wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-6");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-12")) / 2);
    textPoint.setY(mRectMeterGained.volume.y() + 12 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width(), textPoint.y() + 1, mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width() + wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-12");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-24")) / 2);
    textPoint.setY(mRectMeterGained.volume.y() + 24 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width(), textPoint.y() + 1, mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width() + wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-24");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-36")) / 2);
    textPoint.setY(mRectMeterGained.volume.y() + 36 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width(), textPoint.y() + 1, mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width() + wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-36");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-48")) / 2);
    textPoint.setY(mRectMeterGained.volume.y() + 48 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width(), textPoint.y() + 1, mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width() + wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-48");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-60")) / 2);
    textPoint.setY(mRectMeterGained.volume.y() + 60 * pixelPerScale);
    if(!mScaleLineHidden) painter->drawLine(mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width(), textPoint.y() + 1, mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width() + wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-60");
    textPoint.setX(mRectScale.x() + (mRectScale.width() - painter->fontMetrics().horizontalAdvance("-∞")) / 2);
    textPoint.setY(mRectMeterGained.volume.y() + mRectMeterGained.volume.height());
    if(!mScaleLineHidden) painter->drawLine(mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width(), textPoint.y() + 1, mRectMeterOrigin.volume.x() + mRectMeterOrigin.volume.width() + wScaleLine, textPoint.y() + 1);
    painter->drawText(textPoint, "-∞");
    painter->restore();
}


// slot
void VolumeMeterS1M5::in_timerClipOrigin_timeout()
{
    mRectMeterOrigin.clipStatus = false;
    update();
}
void VolumeMeterS1M5::in_timerClipGained_timeout()
{
    mRectMeterGained.clipStatus = false;
    update();
}
void VolumeMeterS1M5::in_mTimerMeter_timeout()
{
    if(mRectMeterOrigin.volumeValue > -90 || mRectMeterGained.volumeValue > -90)
    {
        mRectMeterOrigin.volumeValue = -90 >= mRectMeterOrigin.volumeValue ? (-90) : (qMax(-90, mRectMeterOrigin.volumeValue - 4));
        mRectMeterGained.volumeValue = -90 >= mRectMeterGained.volumeValue ? (-90) : (qMax(-90, mRectMeterGained.volumeValue - 4));
        update();
    }
    else
    {
        mTimerMeter.stop();
    }
}


// setter & getter
void VolumeMeterS1M5::setFont(QFont font)
{
    mFont = font;
    update();
}
void VolumeMeterS1M5::setColorBG(QColor color)
{
    mColorBG = color;
    update();
}
void VolumeMeterS1M5::setValue(int value, int gain)
{
    if(value > 1)
    {
        return;
    }
    if(mTimerMeter.isActive())
    {
        mTimerMeter.stop();
    }
    if(value != 1)
    {
        if(value % 10)
        {
            value = value / 10 - 1;
        }
        else
        {
            value = value / 10;
        }
    }
    int valueGained=value + gain;
    valueGained = qMin(valueGained, 1);
    valueGained = qMax(valueGained, -90);
    value = qMax(-90, value);
    if(value == 1)
    {
        mRectMeterOrigin.clipStatus = true;
        value = 0;
        mRectMeterOrigin.timerClip.start(5000);
    }
    if(valueGained == 1)
    {
        mRectMeterGained.clipStatus = true;
        valueGained = 0;
        mRectMeterGained.timerClip.start(5000);
    }
    mRectMeterOrigin.volumeValue = value >= mRectMeterOrigin.volumeValue ? (value) : (qMax(-90, mRectMeterOrigin.volumeValue - 4));
    mRectMeterGained.volumeValue = valueGained >= mRectMeterGained.volumeValue ? (valueGained) : (qMax(-90, mRectMeterGained.volumeValue - 4));
    update();
}
void VolumeMeterS1M5::setMeterClear()
{
    mTimerMeter.stop();
    mRectMeterOrigin.timerClip.stop();
    mRectMeterGained.timerClip.stop();
    mRectMeterOrigin.volumeValue = -90;
    mRectMeterGained.volumeValue = -90;
    mRectMeterOrigin.clipStatus = false;
    mRectMeterGained.clipStatus = false;
    update();
}
void VolumeMeterS1M5::setMeterSlip()
{
    mTimerMeter.start(55);
}
void VolumeMeterS1M5::setWidthRatio(int scale, int space1, int meterOrigin, int space2, int meterGained)
{
    mScale = scale;
    mSpace1 = space1;
    mMeterOrigin = meterOrigin;
    mSpace2 = space2;
    mMeterGained = meterGained;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
}
void VolumeMeterS1M5::setHeightRatio(int clip, int space1, int volume, int space2)
{
    mHClip = clip;
    mHSpace1 = space1;
    mHVolume = volume;
    mHSpace2 = space2;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
}
void VolumeMeterS1M5::setScaleLineHidden(bool hidden)
{
    mScaleLineHidden = hidden;
    update();
}

