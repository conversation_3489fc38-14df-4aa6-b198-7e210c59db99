#ifndef EQWIDGETIEM_H
#define EQWIDGETIEM_H

#include <QFrame>
#include "eqwidgetitemdata.h"

class QLabel;
class ComboBoxS1M3;
class DialS1M5;
class QCheckBox;
class EqWidgetIem : public QFrame
{
    Q_OBJECT

public:
    explicit EqWidgetIem(int index, QWidget* parent = nullptr);
    ~EqWidgetIem();

    EqWidgetIem& setFont(QFont font);
    void setSizeFactor(double sizeFactor);
    void setItemData(const EqWidgetItemData& data);
    EqWidgetItemData getItemData() const;

    void setItemIndex(int index);
    int getItemIndex() const { return mIndex; }

    void setMinimumItemWidth(int width);

protected:
    void resizeEvent(QResizeEvent* event) override;
    void adjustFontAndSize();

private:
    void setupUI();
    void setupConnections();

    int mIndex;
    EqWidgetItemData mData;

    QFont mFont;
    double mSizeFactor;
    int mMinimumWidth;

    QLabel* mItemLabel;

    ComboBoxS1M3* mTypeComboBox;

    DialS1M5* mGainDial;
    DialS1M5* mFrequencyDial;
    DialS1M5* mQValueDial;

    QCheckBox* mEnabledCheckBox;

signals:
    void dataChanged(int index, const EqWidgetItemData& data);
};

#endif // EQWIDGETIEM_H
