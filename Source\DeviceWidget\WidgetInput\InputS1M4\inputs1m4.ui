<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>InputS1M4</class>
 <widget class="QWidget" name="InputS1M4">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>140</width>
    <height>280</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>80</width>
    <height>220</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="frame">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <widget class="QPushButton" name="pushButtonClose">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>10</y>
        <width>21</width>
        <height>20</height>
       </rect>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QLineEdit" name="lineEdit">
      <property name="geometry">
       <rect>
        <x>9</x>
        <y>10</y>
        <width>91</width>
        <height>21</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignCenter</set>
      </property>
     </widget>
     <widget class="PushButtonGroupS1M9" name="widgetPushButtonGroup1" native="true">
      <property name="geometry">
       <rect>
        <x>80</x>
        <y>40</y>
        <width>21</width>
        <height>61</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>1</width>
        <height>1</height>
       </size>
      </property>
     </widget>
     <widget class="VolumeMeterS1M1" name="widgetVolumeMeter" native="true">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>40</y>
        <width>21</width>
        <height>131</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
     </widget>
     <widget class="DialS1M5" name="widgetDial" native="true">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>180</y>
        <width>90</width>
        <height>41</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
     </widget>
     <widget class="PushButtonGroupS1M3" name="widgetPushButtonGroup2" native="true">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>230</y>
        <width>90</width>
        <height>40</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>6</width>
        <height>7</height>
       </size>
      </property>
     </widget>
     <widget class="QWidget" name="widgetOverlay" native="true">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>40</y>
        <width>21</width>
        <height>21</height>
       </rect>
      </property>
     </widget>
     <widget class="PushButtonGroupS1M5" name="widgetPushButtonGroup3" native="true">
      <property name="geometry">
       <rect>
        <x>80</x>
        <y>110</y>
        <width>21</width>
        <height>61</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>6</width>
        <height>5</height>
       </size>
      </property>
     </widget>
     <zorder>pushButtonClose</zorder>
     <zorder>lineEdit</zorder>
     <zorder>widgetPushButtonGroup1</zorder>
     <zorder>widgetVolumeMeter</zorder>
     <zorder>widgetDial</zorder>
     <zorder>widgetPushButtonGroup2</zorder>
     <zorder>widgetPushButtonGroup3</zorder>
     <zorder>widgetOverlay</zorder>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>VolumeMeterS1M1</class>
   <extends>QWidget</extends>
   <header location="global">volumemeters1m1.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>DialS1M5</class>
   <extends>QWidget</extends>
   <header location="global">dials1m5.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>PushButtonGroupS1M9</class>
   <extends>QWidget</extends>
   <header location="global">pushbuttongroups1m9.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>PushButtonGroupS1M5</class>
   <extends>QWidget</extends>
   <header location="global">pushbuttongroups1m5.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>PushButtonGroupS1M3</class>
   <extends>QWidget</extends>
   <header location="global">pushbuttongroups1m3.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
