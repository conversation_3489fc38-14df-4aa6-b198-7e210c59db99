#ifndef M62_PrivateWidget7_H
#define M62_PrivateWidget7_H

#include <QWidget>
#include "appsettings.h"

namespace Ui {
class M62_PrivateWidget7;
}

class M62_PrivateWidget7 : public QWidget, public AppSettingsObserver
{
    Q_OBJECT

public:
    explicit M62_PrivateWidget7(QWidget *parent = nullptr, const QString &name = "");
    ~M62_PrivateWidget7();
    void setName(const QString &name);
    void setFont(const QFont& font);
    void changeLanguage(QString language);
    void setBluetooth(bool enabled);
    void setBrightness(const QString &brightness);
    void setOtgDirection(bool charging);
    void setUsbCCharging(const QString &charging);
    void setDisplayMode(const QString &mode);
    void setAutoPowerOff(bool enabled);
    void setMainButtonSingleClick(const QString &function);
    void setMainButtonSingleClick(int index);
    void setMainButtonDoubleClick(const QString &function);
    void setMainButtonDoubleClick(int index);
    
    bool isBluetoothEnabled() const;
    QString getBrightness() const;
    bool isOtgCharging() const;
    QString usbCCharging() const;
    QString getDisplayMode() const;
    bool isAutoPowerOff() const;
    QString getMainButtonSingleClick() const;
    QString getMainButtonDoubleClick() const;
    int itemCount()const;

protected:
    void resizeEvent(QResizeEvent *event) override;
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;

signals:
    void attributeChanged(const QString &objectName, const QString &attribute, const QString &value);

private:
    Ui::M62_PrivateWidget7 *ui;
    QFont m_font;
    QString m_curMode;
    QString m_language;
};

#endif // M62_PrivateWidget7_H
